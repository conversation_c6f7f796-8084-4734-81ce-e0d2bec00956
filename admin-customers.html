<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customers - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles (Same as admin.html) */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        /* Sidebar Styles */
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        /* Main Content Styles */
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Customers Page Specific Styles */
        .customers-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select,
        .search-box input {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-box button {
            padding: 8px 12px;
            border: none;
            background-color: var(--primary-color);
            color: white;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }

        .customers-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .customers-table th,
        .customers-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .customers-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }

        .customers-table tr:last-child td {
            border-bottom: none;
        }

        .customer-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .customer-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
            display: inline-block;
        }

        .customer-status.active {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }

        .customer-status.inactive {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }

        .customer-actions {
            display: flex;
            gap: 10px;
        }

        .customer-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .customer-actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:hover:not(.active) {
            background-color: var(--bg-color);
        }

        /* Customer Details Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            padding: 50px 0;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: 10px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 5px 20px var(--shadow-color);
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            opacity: 1;
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .customer-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .customer-detail-block h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .customer-detail-block p {
            margin-bottom: 5px;
        }

        .customer-orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .customer-orders-table th,
        .customer-orders-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .customer-orders-table th {
            font-weight: 600;
        }

        .customer-orders-table tr:last-child td {
            border-bottom: none;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group.full-width {
            grid-column: span 2;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-cancel {
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .customer-details-grid,
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-group.full-width {
                grid-column: span 1;
            }
        }

        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }
            
            .customers-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .customers-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="admin-customers.html" class="active"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="#" data-section="categories"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="#" data-section="inventory"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="#" data-section="analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Customers Section -->
                <div class="admin-section" id="customers-section">
                    <div class="admin-header">
                        <h1>Customers</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small add-customer-btn"><i class="fas fa-plus"></i> Add Customer</button>
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                        </div>
                    </div>
                    
                    <!-- Customers Filters -->
                    <div class="customers-filters">
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter">
                                <option value="all">All</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date-filter">Joined:</label>
                            <select id="date-filter">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="year">This Year</option>
                            </select>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" placeholder="Search customers...">
                            <button><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                    
                    <!-- Customers Table -->
                    <table class="customers-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Joined</th>
                                <th>Orders</th>
                                <th>Spent</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>#C001</td>
                                <td class="customer-name">John Doe</td>
                                <td><EMAIL></td>
                                <td>June 10, 2023</td>
                                <td>5</td>
                                <td>$345.75</td>
                                <td><span class="customer-status active">Active</span></td>
                                <td class="customer-actions">
                                    <button title="View" class="view-customer" data-id="C001"><i class="fas fa-eye"></i></button>
                                    <button title="Edit" class="edit-customer" data-id="C001"><i class="fas fa-edit"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>#C002</td>
                                <td class="customer-name">Jane Smith</td>
                                <td><EMAIL></td>
                                <td>June 12, 2023</td>
                                <td>2</td>
                                <td>$89.50</td>
                                <td><span class="customer-status active">Active</span></td>
                                <td class="customer-actions">
                                    <button title="View" class="view-customer" data-id="C002"><i class="fas fa-eye"></i></button>
                                    <button title="Edit" class="edit-customer" data-id="C002"><i class="fas fa-edit"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>#C003</td>
                                <td class="customer-name">Robert Johnson</td>
                                <td><EMAIL></td>
                                <td>June 14, 2023</td>
                                <td>1</td>
                                <td>$210.75</td>
                                <td><span class="customer-status active">Active</span></td>
                                <td class="customer-actions">
                                    <button title="View" class="view-customer" data-id="C003"><i class="fas fa-eye"></i></button>
                                    <button title="Edit" class="edit-customer" data-id="C003"><i class="fas fa-edit"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>#C004</td>
                                <td class="customer-name">Emily Davis</td>
                                <td><EMAIL></td>
                                <td>June 15, 2023</td>
                                <td>1</td>
                                <td>$45.99</td>
                                <td><span class="customer-status active">Active</span></td>
                                <td class="customer-actions">
                                    <button title="View" class="view-customer" data-id="C004"><i class="fas fa-eye"></i></button>
                                    <button title="Edit" class="edit-customer" data-id="C004"><i class="fas fa-edit"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td>#C005</td>
                                <td class="customer-name">Michael Wilson</td>
                                <td><EMAIL></td>
                                <td>May 30, 2023</td>
                                <td>0</td>
                                <td>$0.00</td>
                                <td><span class="customer-status inactive">Inactive</span></td>
                                <td class="customer-actions">
                                    <button title="View" class="view-customer" data-id="C005"><i class="fas fa-eye"></i></button>
                                    <button title="Edit" class="edit-customer" data-id="C005"><i class="fas fa-edit"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Customer Details Modal -->
        <div class="modal" id="customer-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Customer Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="customer-details-grid">
                        <div class="customer-detail-block">
                            <h3>Personal Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                            <p><strong>Joined:</strong> <span id="modal-customer-joined">June 10, 2023</span></p>
                            <p><strong>Status:</strong> <span id="modal-customer-status" class="customer-status active">Active</span></p>
                        </div>
                        <div class="customer-detail-block">
                            <h3>Shipping Address</h3>
                            <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                            
                            <h3>Billing Address</h3>
                            <p id="modal-billing-address">123 Anime Street, Tokyo, JP 100-0001</p>
                        </div>
                    </div>
                    
                    <div class="customer-detail-block">
                        <h3>Order History</h3>
                        <table class="customer-orders-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Date</th>
                                    <th>Items</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#INF12345</td>
                                    <td>June 15, 2023</td>
                                    <td>3</td>
                                    <td>$125.99</td>
                                    <td><span class="status completed">Completed</span></td>
                                </tr>
                                <tr>
                                    <td>#INF12340</td>
                                    <td>June 10, 2023</td>
                                    <td>2</td>
                                    <td>$89.50</td>
                                    <td><span class="status completed">Completed</span></td>
                                </tr>
                                <tr>
                                    <td>#INF12335</td>
                                    <td>June 5, 2023</td>
                                    <td>1</td>
                                    <td>$45.99</td>
                                    <td><span class="status completed">Completed</span></td>
                                </tr>
                                <tr>
                                    <td>#INF12330</td>
                                    <td>May 28, 2023</td>
                                    <td>2</td>
                                    <td>$84.27</td>
                                    <td><span class="status completed">Completed</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="customer-detail-block">
                        <h3>Notes</h3>
                        <p id="modal-customer-notes">Frequent buyer, prefers express shipping. Interested in limited edition items.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Close</button>
                    <button class="btn btn-small edit-customer-btn" data-id="C001">Edit Customer</button>
                </div>
            </div>
        </div>
        
        <!-- Edit Customer Modal -->
        <div class="modal" id="edit-customer-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Edit Customer</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <form id="customer-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="edit-customer-name">Name</label>
                                <input type="text" id="edit-customer-name" value="John Doe" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-customer-email">Email</label>
                                <input type="email" id="edit-customer-email" value="<EMAIL>" required>
                            </div>
                            <div class="form-group">
                                <label for="edit-customer-phone">Phone</label>
                                <input type="tel" id="edit-customer-phone" value="+****************">
                            </div>
                            <div class="form-group">
                                <label for="edit-customer-status">Status</label>
                                <select id="edit-customer-status">
                                    <option value="active" selected>Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label for="edit-shipping-address">Shipping Address</label>
                                <textarea id="edit-shipping-address" rows="3">123 Anime Street, Tokyo, JP 100-0001</textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="edit-billing-address">Billing Address</label>
                                <textarea id="edit-billing-address" rows="3">123 Anime Street, Tokyo, JP 100-0001</textarea>
                            </div>
                            <div class="form-group full-width">
                                <label for="edit-customer-notes">Notes</label>
                                <textarea id="edit-customer-notes" rows="3">Frequent buyer, prefers express shipping. Interested in limited edition items.</textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small" form="customer-form">Save Changes</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.