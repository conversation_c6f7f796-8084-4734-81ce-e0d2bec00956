<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        /* Sidebar Styles */
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        /* Main Content Styles */
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }
        
        /* Dashboard Styles */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-card .stat-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 15px;
        }
        
        .stat-card:nth-child(1) .stat-icon {
            background-color: rgba(106, 61, 232, 0.2);
            color: var(--primary-color);
        }
        
        .stat-card:nth-child(2) .stat-icon {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }
        
        .stat-card:nth-child(3) .stat-icon {
            background-color: rgba(255, 190, 11, 0.2);
            color: var(--accent-color);
        }
        
        .stat-card:nth-child(4) .stat-icon {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }
        
        .stat-card h3 {
            font-size: 0.9rem;
            margin-bottom: 10px;
            opacity: 0.8;
        }
        
        .stat-card .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }
        
        .stat-card .stat-change {
            font-size: 0.9rem;
            display: flex;
            align-items: center;
        }
        
        .stat-card .stat-change.positive {
            color: #48c774;
        }
        
        .stat-card .stat-change.negative {
            color: var(--secondary-color);
        }
        
        .stat-card .stat-change i {
            margin-right: 5px;
        }
        
        /* Charts Section */
        .charts-row {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .chart-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }
        
        .chart-card h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .chart-card h3 .chart-period {
            font-size: 0.9rem;
            font-weight: normal;
            color: var(--primary-color);
        }
        
        .chart-placeholder {
            height: 300px;
            background-color: var(--bg-color);
            border-radius: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-color);
            opacity: 0.7;
        }
        
        /* Recent Orders Table */
        .recent-orders {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 30px;
        }
        
        .recent-orders h3 {
            font-size: 1.2rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recent-orders h3 a {
            font-size: 0.9rem;
            font-weight: normal;
        }
        
        .orders-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .orders-table th,
        .orders-table td {
            padding: 12px 15px;
            text-align: left;
        }
        
        .orders-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }
        
        .orders-table tr {
            border-bottom: 1px solid var(--border-color);
        }
        
        .orders-table tr:last-child {
            border-bottom: none;
        }
        
        .orders-table .order-id {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .orders-table .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
            display: inline-block;
        }
        
        .orders-table .status.completed {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }
        
        .orders-table .status.processing {
            background-color: rgba(255, 190, 11, 0.2);
            color: var(--accent-color);
        }
        
        .orders-table .status.cancelled {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }
        
        .orders-table .actions {
            display: flex;
            gap: 10px;
        }
        
        .orders-table .actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }
        
        .orders-table .actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }
        
        /* Responsive Styles */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 992px) {
            .charts-row {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
        }
        
        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .admin-content {
                padding: 20px 15px;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="#" class="active" data-section="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html" data-section="products"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html" data-section="orders"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="admin-customers.html" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="#" data-section="categories"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="#" data-section="inventory"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="#" data-section="analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Dashboard Section -->
                <div class="admin-section" id="dashboard-section">
                    <div class="admin-header">
                        <h1>Dashboard</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                            <button class="btn btn-small"><i class="fas fa-sync-alt"></i> Refresh</button>
                        </div>
                    </div>
                    
                    <!-- Stats Cards -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <h3>Total Orders</h3>
                            <div class="stat-value">1,254</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> 12.5% from last month
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <h3>Total Revenue</h3>
                            <div class="stat-value">$45,678</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> 8.3% from last month
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3>Total Customers</h3>
                            <div class="stat-value">892</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i> 5.7% from last month
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <h3>Total Products</h3>
                            <div class="stat-value">156</div>
                            <div class="stat-change negative">
                                <i class="fas fa-arrow-down"></i> 2.1% from last month
                            </div>
                        </div>
                    </div>
                    
                    <!-- Charts -->
                    <div class="charts-row">
                        <div class="chart-card">
                            <h3>
                                Sales Overview
                                <span class="chart-period">Last 30 days</span>
                            </h3>
                            <div class="chart-placeholder">
                                <p>Sales Chart Will Be Displayed Here</p>
                            </div>
                        </div>
                        
                        <div class="chart-card">
                            <h3>
                                Top Categories
                                <span class="chart-period">This month</span>
                            </h3>
                            <div class="chart-placeholder">
                                <p>Categories Chart Will Be Displayed Here</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Orders -->
                    <div class="recent-orders">
                        <h3>
                            Recent Orders
                            <a href="#" data-section="orders">View All</a>
                        </h3>
                        <table class="orders-table">
                            <thead>
                                <tr>
                                    <th>Order ID</th>
                                    <th>Customer</th>
                                    <th>Date</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="order-id">#INF12345</td>
                                    <td>John Doe</td>
                                    <td>June 15, 2023</td>
                                    <td>$125.99</td>
                                    <td><span class="status completed">Completed</span></td>
                                    <td class="actions">
                                        <button title="View"><i class="fas fa-eye"></i></button>
                                        <button title="Edit"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="order-id">#INF12346</td>
                                    <td>Jane Smith</td>
                                    <td>June 14, 2023</td>
                                    <td>$89.50</td>
                                    <td><span class="status processing">Processing</span></td>
                                    <td class="actions">
                                        <button title="View"><i class="fas fa-eye"></i></button>
                                        <button title="Edit"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="order-id">#INF12347</td>
                                    <td>Robert Johnson</td>
                                    <td>June 14, 2023</td>
                                    <td>$210.75</td>
                                    <td><span class="status cancelled">Cancelled</span></td>
                                    <td class="actions">
                                        <button title="View"><i class="fas fa-eye"></i></button>
                                        <button title="Edit"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="order-id">#INF12348</td>
                                    <td>Emily Davis</td>
                                    <td>June 13, 2023</td>
                                    <td>$45.99</td>
                                    <td><span class="status completed">Completed</span></td>
                                    <td class="actions">
                                        <button title="View"><i class="fas fa-eye"></i></button>
                                        <button title="Edit"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="order-id">#INF12349</td>
                                    <td>Michael Wilson</td>
                                    <td>June 13, 2023</td>
                                    <td>$175.25</td>
                                    <td><span class="status processing">Processing</span></td>
                                    <td class="actions">
                                        <button title="View"><i class="fas fa-eye"></i></button>
                                        <button title="Edit"><i class="fas fa-edit"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>