<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
            min-height: calc(100vh - 76px);
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Settings Navigation */
        .settings-nav {
            display: flex;
            gap: 20px;
            margin-bottom: 30px;
            border-bottom: 1px solid var(--border-color);
        }

        .settings-nav button {
            background: none;
            border: none;
            padding: 15px 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-color);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .settings-nav button.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        /* Settings Sections */
        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .settings-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .settings-card h3 {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .settings-card p {
            opacity: 0.8;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info h4 {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .setting-info p {
            font-size: 0.9rem;
            opacity: 0.7;
            margin: 0;
        }

        .color-picker {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option.selected {
            border-color: var(--text-color);
            transform: scale(1.1);
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: var(--bg-color);
            border-radius: 8px;
            margin-bottom: 10px;
        }

        .backup-info h4 {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .backup-info p {
            font-size: 0.9rem;
            opacity: 0.7;
            margin: 0;
        }

        .backup-actions {
            display: flex;
            gap: 10px;
        }

        .backup-actions button {
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-download {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-restore {
            background-color: var(--accent-color);
            color: white;
        }

        .btn-delete {
            background-color: var(--secondary-color);
            color: white;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }

            .settings-nav {
                flex-wrap: wrap;
                gap: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .admin-content {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .settings-nav {
                flex-direction: column;
                gap: 0;
            }

            .settings-nav button {
                text-align: left;
                padding: 10px 0;
            }

            .setting-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>

                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="admin-analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html" class="active"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>

            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-section" id="settings-section">
                    <div class="admin-header">
                        <h1>Settings</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small"><i class="fas fa-save"></i> Save All</button>
                            <button class="btn btn-small"><i class="fas fa-undo"></i> Reset</button>
                        </div>
                    </div>

                    <!-- Settings Navigation -->
                    <div class="settings-nav">
                        <button class="settings-tab active" data-tab="general">General</button>
                        <button class="settings-tab" data-tab="store">Store</button>
                        <button class="settings-tab" data-tab="payment">Payment</button>
                        <button class="settings-tab" data-tab="notifications">Notifications</button>
                        <button class="settings-tab" data-tab="security">Security</button>
                        <button class="settings-tab" data-tab="backup">Backup</button>
                    </div>

                    <!-- General Settings -->
                    <div class="settings-section active" id="general-settings">
                        <div class="settings-card">
                            <h3>Site Information</h3>
                            <p>Basic information about your store</p>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="site-name">Site Name</label>
                                    <input type="text" id="site-name" value="Infinite Shadow">
                                </div>
                                <div class="form-group">
                                    <label for="site-tagline">Tagline</label>
                                    <input type="text" id="site-tagline" value="Your ultimate anime merchandise store">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="site-description">Description</label>
                                <textarea id="site-description">Discover the best anime merchandise, figures, clothing, and collectibles at Infinite Shadow. Your one-stop shop for all things anime.</textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="admin-email">Admin Email</label>
                                    <input type="email" id="admin-email" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="support-email">Support Email</label>
                                    <input type="email" id="support-email" value="<EMAIL>">
                                </div>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Appearance</h3>
                            <p>Customize the look and feel of your store</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Dark Mode</h4>
                                    <p>Enable dark theme for the admin panel</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="dark-mode-toggle" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="form-group">
                                <label>Primary Color</label>
                                <div class="color-picker">
                                    <div class="color-option selected" style="background: #6a3de8;" data-color="#6a3de8"></div>
                                    <div class="color-option" style="background: #3273dc;" data-color="#3273dc"></div>
                                    <div class="color-option" style="background: #48c774;" data-color="#48c774"></div>
                                    <div class="color-option" style="background: #ffbe0b;" data-color="#ffbe0b"></div>
                                    <div class="color-option" style="background: #ff6b6b;" data-color="#ff6b6b"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Store Settings -->
                    <div class="settings-section" id="store-settings">
                        <div class="settings-card">
                            <h3>Store Configuration</h3>
                            <p>Configure your store's basic settings</p>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="currency">Currency</label>
                                    <select id="currency">
                                        <option value="USD" selected>USD ($)</option>
                                        <option value="EUR">EUR (€)</option>
                                        <option value="GBP">GBP (£)</option>
                                        <option value="JPY">JPY (¥)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="timezone">Timezone</label>
                                    <select id="timezone">
                                        <option value="UTC" selected>UTC</option>
                                        <option value="EST">Eastern Time</option>
                                        <option value="PST">Pacific Time</option>
                                        <option value="JST">Japan Standard Time</option>
                                    </select>
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Maintenance Mode</h4>
                                    <p>Put your store in maintenance mode</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="maintenance-mode">
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Guest Checkout</h4>
                                    <p>Allow customers to checkout without creating an account</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="guest-checkout" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Shipping Settings</h3>
                            <p>Configure shipping options and rates</p>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="free-shipping-threshold">Free Shipping Threshold</label>
                                    <input type="number" id="free-shipping-threshold" value="50" step="0.01">
                                </div>
                                <div class="form-group">
                                    <label for="standard-shipping-rate">Standard Shipping Rate</label>
                                    <input type="number" id="standard-shipping-rate" value="5.99" step="0.01">
                                </div>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>International Shipping</h4>
                                    <p>Enable shipping to international destinations</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="international-shipping" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Settings -->
                    <div class="settings-section" id="payment-settings">
                        <div class="settings-card">
                            <h3>Payment Methods</h3>
                            <p>Configure accepted payment methods</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Credit Cards</h4>
                                    <p>Accept Visa, MasterCard, American Express</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="credit-cards" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>PayPal</h4>
                                    <p>Accept payments through PayPal</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="paypal" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Apple Pay</h4>
                                    <p>Accept payments through Apple Pay</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="apple-pay">
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="payment-gateway">Payment Gateway</label>
                                <select id="payment-gateway">
                                    <option value="stripe" selected>Stripe</option>
                                    <option value="paypal">PayPal</option>
                                    <option value="square">Square</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications Settings -->
                    <div class="settings-section" id="notifications-settings">
                        <div class="settings-card">
                            <h3>Email Notifications</h3>
                            <p>Configure when to send email notifications</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>New Order Notifications</h4>
                                    <p>Receive email when new orders are placed</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="new-order-emails" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Low Stock Alerts</h4>
                                    <p>Receive email when products are low in stock</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="low-stock-emails" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Customer Registration</h4>
                                    <p>Receive email when new customers register</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="customer-registration-emails">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Customer Notifications</h3>
                            <p>Configure customer email notifications</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Order Confirmations</h4>
                                    <p>Send order confirmation emails to customers</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="order-confirmations" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Shipping Updates</h4>
                                    <p>Send shipping status updates to customers</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="shipping-updates" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="settings-section" id="security-settings">
                        <div class="settings-card">
                            <h3>Account Security</h3>
                            <p>Manage your account security settings</p>

                            <div class="form-group">
                                <label for="current-password">Current Password</label>
                                <input type="password" id="current-password" placeholder="Enter current password">
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="new-password">New Password</label>
                                    <input type="password" id="new-password" placeholder="Enter new password">
                                </div>
                                <div class="form-group">
                                    <label for="confirm-password">Confirm Password</label>
                                    <input type="password" id="confirm-password" placeholder="Confirm new password">
                                </div>
                            </div>

                            <button class="btn btn-small">Update Password</button>
                        </div>

                        <div class="settings-card">
                            <h3>Two-Factor Authentication</h3>
                            <p>Add an extra layer of security to your account</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Enable 2FA</h4>
                                    <p>Require authentication code from your phone</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="two-factor-auth">
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Session Management</h3>
                            <p>Manage active sessions and login attempts</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Auto Logout</h4>
                                    <p>Automatically logout after 30 minutes of inactivity</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="auto-logout" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <button class="btn btn-small btn-secondary">View Active Sessions</button>
                        </div>
                    </div>

                    <!-- Backup Settings -->
                    <div class="settings-section" id="backup-settings">
                        <div class="settings-card">
                            <h3>Automatic Backups</h3>
                            <p>Configure automatic backup settings</p>

                            <div class="setting-item">
                                <div class="setting-info">
                                    <h4>Enable Auto Backup</h4>
                                    <p>Automatically backup your data daily</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" id="auto-backup" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>

                            <div class="form-group">
                                <label for="backup-frequency">Backup Frequency</label>
                                <select id="backup-frequency">
                                    <option value="daily" selected>Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>

                            <button class="btn btn-small">Create Backup Now</button>
                        </div>

                        <div class="settings-card">
                            <h3>Backup History</h3>
                            <p>Manage your backup files</p>

                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>Full Backup - December 15, 2023</h4>
                                    <p>Size: 45.2 MB • Products, Orders, Customers</p>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn-download">Download</button>
                                    <button class="btn-restore">Restore</button>
                                    <button class="btn-delete">Delete</button>
                                </div>
                            </div>

                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>Full Backup - December 14, 2023</h4>
                                    <p>Size: 44.8 MB • Products, Orders, Customers</p>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn-download">Download</button>
                                    <button class="btn-restore">Restore</button>
                                    <button class="btn-delete">Delete</button>
                                </div>
                            </div>

                            <div class="backup-item">
                                <div class="backup-info">
                                    <h4>Full Backup - December 13, 2023</h4>
                                    <p>Size: 44.1 MB • Products, Orders, Customers</p>
                                </div>
                                <div class="backup-actions">
                                    <button class="btn-download">Download</button>
                                    <button class="btn-restore">Restore</button>
                                    <button class="btn-delete">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Settings tab functionality
            const settingsTabs = document.querySelectorAll('.settings-tab');
            const settingsSections = document.querySelectorAll('.settings-section');

            settingsTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // Remove active class from all tabs and sections
                    settingsTabs.forEach(t => t.classList.remove('active'));
                    settingsSections.forEach(s => s.classList.remove('active'));

                    // Add active class to clicked tab and corresponding section
                    this.classList.add('active');
                    document.getElementById(targetTab + '-settings').classList.add('active');
                });
            });

            // Color picker functionality
            const colorOptions = document.querySelectorAll('.color-option');
            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    colorOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');

                    const selectedColor = this.getAttribute('data-color');
                    // Update CSS custom property
                    document.documentElement.style.setProperty('--primary-color', selectedColor);

                    // Save to localStorage
                    localStorage.setItem('primaryColor', selectedColor);
                });
            });

            // Load saved color
            const savedColor = localStorage.getItem('primaryColor');
            if (savedColor) {
                document.documentElement.style.setProperty('--primary-color', savedColor);
                colorOptions.forEach(option => {
                    option.classList.remove('selected');
                    if (option.getAttribute('data-color') === savedColor) {
                        option.classList.add('selected');
                    }
                });
            }

            // Dark mode toggle
            const darkModeToggle = document.getElementById('dark-mode-toggle');
            darkModeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('theme', 'dark-mode');
                } else {
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('theme', '');
                }
            });

            // Password update functionality
            const updatePasswordBtn = document.querySelector('.settings-card button');
            if (updatePasswordBtn && updatePasswordBtn.textContent.includes('Update Password')) {
                updatePasswordBtn.addEventListener('click', function() {
                    const currentPassword = document.getElementById('current-password').value;
                    const newPassword = document.getElementById('new-password').value;
                    const confirmPassword = document.getElementById('confirm-password').value;

                    if (!currentPassword || !newPassword || !confirmPassword) {
                        alert('Please fill in all password fields');
                        return;
                    }

                    if (newPassword !== confirmPassword) {
                        alert('New passwords do not match');
                        return;
                    }

                    if (newPassword.length < 8) {
                        alert('Password must be at least 8 characters long');
                        return;
                    }

                    alert('Password updated successfully!');
                    // Clear password fields
                    document.getElementById('current-password').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';
                });
            }

            // Backup functionality
            document.querySelectorAll('.btn-download').forEach(button => {
                button.addEventListener('click', function() {
                    const backupName = this.closest('.backup-item').querySelector('h4').textContent;
                    alert(`Downloading ${backupName}...`);
                });
            });

            document.querySelectorAll('.btn-restore').forEach(button => {
                button.addEventListener('click', function() {
                    const backupName = this.closest('.backup-item').querySelector('h4').textContent;
                    if (confirm(`Are you sure you want to restore from ${backupName}? This will overwrite current data.`)) {
                        alert(`Restoring from ${backupName}...`);
                    }
                });
            });

            document.querySelectorAll('.btn-delete').forEach(button => {
                button.addEventListener('click', function() {
                    const backupName = this.closest('.backup-item').querySelector('h4').textContent;
                    if (confirm(`Are you sure you want to delete ${backupName}?`)) {
                        this.closest('.backup-item').remove();
                        alert(`${backupName} deleted successfully.`);
                    }
                });
            });

            // Create backup now button
            const createBackupBtn = document.querySelector('.settings-card button');
            if (createBackupBtn && createBackupBtn.textContent.includes('Create Backup Now')) {
                createBackupBtn.addEventListener('click', function() {
                    alert('Creating backup... This may take a few minutes.');
                });
            }

            // Save all settings
            const saveAllBtn = document.querySelector('.admin-actions .btn:first-child');
            if (saveAllBtn) {
                saveAllBtn.addEventListener('click', function() {
                    alert('All settings saved successfully!');
                });
            }

            // Reset settings
            const resetBtn = document.querySelector('.admin-actions .btn:last-child');
            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    if (confirm('Are you sure you want to reset all settings to default values?')) {
                        alert('Settings reset to default values.');
                        location.reload();
                    }
                });
            }

            // Mobile menu toggle
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');

            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
