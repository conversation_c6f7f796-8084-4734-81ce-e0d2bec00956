<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles (Same as admin.html) */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        /* Sidebar Styles */
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        /* Main Content Styles */
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Products Page Specific Styles */
        .products-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select,
        .search-box input {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-box button {
            padding: 8px 12px;
            border: none;
            background-color: var(--primary-color);
            color: white;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }

        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .products-table th,
        .products-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .products-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }

        .products-table tr:last-child td {
            border-bottom: none;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 5px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .product-category {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
        }

        .product-stock {
            font-weight: 600;
        }

        .product-stock.in-stock {
            color: #48c774;
        }

        .product-stock.low-stock {
            color: var(--accent-color);
        }

        .product-stock.out-of-stock {
            color: var(--secondary-color);
        }

        .product-actions {
            display: flex;
            gap: 10px;
        }

        .product-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .product-actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:hover:not(.active) {
            background-color: var(--bg-color);
        }

        /* Add/Edit Product Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            padding: 50px 0;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: 10px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 5px 20px var(--shadow-color);
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            opacity: 1;
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group.full-width {
            grid-column: span 2;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .image-upload {
            border: 2px dashed var(--border-color);
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .image-upload:hover {
            border-color: var(--primary-color);
        }

        .image-upload i {
            font-size: 2rem;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .image-upload p {
            margin-bottom: 0;
            font-size: 0.9rem;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-cancel {
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-group.full-width {
                grid-column: span 1;
            }
        }

        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }
            
            .products-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .products-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .products-table th,
            .products-table td {
                min-width: 120px;
            }

            .product-image {
                width: 30px;
                height: 30px;
            }
        }

        @media (max-width: 480px) {
            .admin-content {
                padding: 15px;
            }

            .products-filters {
                gap: 10px;
            }

            .filter-group {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }

            .search-box {
                width: 100%;
            }

            .search-box input {
                flex: 1;
            }

            .products-table th,
            .products-table td {
                min-width: 100px;
                padding: 8px;
                font-size: 0.9rem;
            }

            .product-image {
                width: 25px;
                height: 25px;
            }

            .product-actions {
                flex-direction: column;
                gap: 5px;
            }

            .product-actions button {
                font-size: 0.8rem;
                padding: 4px 8px;
            }

            .modal-content {
                margin: 0 10px;
                max-width: calc(100% - 20px);
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-footer button {
                width: 100%;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html" class="active"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="#" data-section="categories"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="#" data-section="inventory"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="#" data-section="analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Products Section -->
                <div class="admin-section" id="products-section">
                    <div class="admin-header">
                        <h1>Products</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small add-product-btn"><i class="fas fa-plus"></i> Add Product</button>
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                        </div>
                    </div>
                    
                    <!-- Products Filters -->
                    <div class="products-filters">
                        <div class="filter-group">
                            <label for="category-filter">Category:</label>
                            <select id="category-filter">
                                <option value="all">All Categories</option>
                                <option value="figures">Figures</option>
                                <option value="clothing">Clothing</option>
                                <option value="accessories">Accessories</option>
                                <option value="manga">Manga</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter">
                                <option value="all">All</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" placeholder="Search products...">
                            <button><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                    
                    <!-- Products Table -->
                    <table class="products-table">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Product</th>
                                <th>Category</th>
                                <th>Price</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><img src="images/1.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Naruto Uzumaki Figure</td>
                                <td><span class="product-category">Figures</span></td>
                                <td>$59.99</td>
                                <td><span class="product-stock in-stock">25 in stock</span></td>
                                <td>Active</td>
                                <td class="product-actions">
                                    <button title="Edit"><i class="fas fa-edit"></i></button>
                                    <button title="Delete"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/2.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Attack on Titan Hoodie</td>
                                <td><span class="product-category">Clothing</span></td>
                                <td>$39.99</td>
                                <td><span class="product-stock low-stock">5 in stock</span></td>
                                <td>Active</td>
                                <td class="product-actions">
                                    <button title="Edit"><i class="fas fa-edit"></i></button>
                                    <button title="Delete"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/3.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">My Hero Academia Backpack</td>
                                <td><span class="product-category">Accessories</span></td>
                                <td>$45.99</td>
                                <td><span class="product-stock in-stock">18 in stock</span></td>
                                <td>Active</td>
                                <td class="product-actions">
                                    <button title="Edit"><i class="fas fa-edit"></i></button>
                                    <button title="Delete"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/4.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Demon Slayer Poster Set</td>
                                <td><span class="product-category">Accessories</span></td>
                                <td>$24.99</td>
                                <td><span class="product-stock out-of-stock">Out of stock</span></td>
                                <td>Inactive</td>
                                <td class="product-actions">
                                    <button title="Edit"><i class="fas fa-edit"></i></button>
                                    <button title="Delete"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/7.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">One Piece Manga Collection</td>
                                <td><span class="product-category">Manga</span></td>
                                <td>$120.00</td>
                                <td><span class="product-stock in-stock">12 in stock</span></td>
                                <td>Active</td>
                                <td class="product-actions">
                                    <button title="Edit"><i class="fas fa-edit"></i></button>
                                    <button title="Delete"><i class="fas fa-trash"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Add/Edit Product Modal -->
        <div class="modal" id="product-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add New Product</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <form id="product-form">
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="product-name">Product Name</label>
                                <input type="text" id="product-name" required>
                            </div>
                            <div class="form-group">
                                <label for="product-category">Category</label>
                                <select id="product-category" required>
                                    <option value="">Select Category</option>
                                    <option value="figures">Figures</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="accessories">Accessories</option>
                                    <option value="manga">Manga</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="product-price">Price ($)</label>
                                <input type="number" id="product-price" step="0.01" min="0" required>
                            </div>
                            <div class="form-group">
                                <label for="product-stock">Stock Quantity</label>
                                <input type="number" id="product-stock" min="0" required>
                            </div>
                            <div class="form-group full-width">
                                <label for="product-description">Description</label>
                                <textarea id="product-description" required></textarea>
                            </div>
                            <div class="form-group">
                                <label for="product-status">Status</label>
                                <select id="product-status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="product-featured">Featured</label>
                                <select id="product-featured">
                                    <option value="no">No</option>
                                    <option value="yes">Yes</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label>Product Images</label>
                                <div class="image-upload">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>Drag & drop images here or click to browse</p>
                                    <input type="file" id="product-images" multiple style="display: none;">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small" form="product-form">Save Product</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Product Modal Functionality
            const addProductBtn = document.querySelector('.add-product-btn');
            const productModal = document.getElementById('product-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Open modal when Add Product button is clicked
            addProductBtn.addEventListener('click', function() {
                productModal.classList.add('active');
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                productModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                productModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            productModal.addEventListener('click', function(e) {
                if (e.target === productModal) {
                    productModal.classList.remove('active');
                }
            });
            
            // Image upload functionality
            const imageUpload = document.querySelector('.image-upload');
            const imageInput = document.getElementById('product-images');
            
            imageUpload.addEventListener('click', function() {
                imageInput.click();
            });
            
            // Product form submission (placeholder)
            const productForm = document.getElementById('product-form');
            
            productForm.addEventListener('submit', function(e) {
                e.preventDefault();
                alert('Product saved successfully!');
                productModal.classList.remove('active');
            });
            
            // Edit product buttons (placeholder)
            const editButtons = document.querySelectorAll('.product-actions button:first-child');
            
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.closest('tr').querySelector('.product-name').textContent;
                    document.querySelector('.modal-header h2').textContent = 'Edit Product';
                    document.getElementById('product-name').value = productName;
                    productModal.classList.add('active');
                });
            });
            
            // Delete product buttons (placeholder)
            const deleteButtons = document.querySelectorAll('.product-actions button:last-child');
            
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.closest('tr').querySelector('.product-name').textContent;
                    if (confirm(`Are you sure you want to delete ${productName}?`)) {
                        alert(`${productName} has been deleted.`);
                        // In a real application, this would remove the product from the database
                        this.closest('tr').remove();
                    }
                });
            });

            // Filter functionality
            const categoryFilter = document.getElementById('category-filter');
            const statusFilter = document.getElementById('status-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');

            function filterProducts() {
                const categoryValue = categoryFilter.value;
                const statusValue = statusFilter.value;
                const searchValue = searchBox.value.toLowerCase();

                const rows = document.querySelectorAll('.products-table tbody tr');

                rows.forEach(row => {
                    const productCategory = row.querySelectorAll('td')[2].textContent.toLowerCase(); // Category column
                    const productStatus = row.querySelectorAll('td')[5].textContent.toLowerCase(); // Status column
                    const productName = row.querySelector('.product-name').textContent.toLowerCase();

                    let showByCategory = categoryValue === 'all' || productCategory === categoryValue;
                    let showByStatus = statusValue === 'all' || productStatus === statusValue;
                    let showBySearch = searchValue === '' ||
                                      productName.includes(searchValue);

                    if (showByCategory && showByStatus && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            categoryFilter.addEventListener('change', filterProducts);
            statusFilter.addEventListener('change', filterProducts);
            searchButton.addEventListener('click', filterProducts);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterProducts();
                }
            });

            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');

            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
</body>
</html>