<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categories - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
            min-height: calc(100vh - 76px);
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Categories Grid */
        .categories-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .category-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            transition: transform 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-5px);
        }

        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .category-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 10px;
        }

        .category-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .category-description {
            color: var(--text-color);
            opacity: 0.7;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .category-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            opacity: 0.7;
        }

        .category-actions {
            display: flex;
            gap: 10px;
        }

        .category-actions button {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn-edit {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-delete {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-edit:hover {
            background-color: #5a2bc7;
        }

        .btn-delete:hover {
            background-color: #ff5252;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            padding: 50px 0;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: 10px;
            max-width: 600px;
            margin: 0 auto;
            box-shadow: 0 5px 20px var(--shadow-color);
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            opacity: 1;
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .color-picker-group {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option.selected {
            border-color: var(--text-color);
            transform: scale(1.1);
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-cancel {
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }

            .categories-grid {
                grid-template-columns: 1fr;
            }

            .admin-content {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                margin: 0 10px;
                max-width: calc(100% - 20px);
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-footer button {
                width: 100%;
            }

            .color-picker-group {
                grid-template-columns: repeat(4, 1fr);
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html" class="active"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="admin-analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>

            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-section" id="categories-section">
                    <div class="admin-header">
                        <h1>Categories</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small add-category-btn"><i class="fas fa-plus"></i> Add Category</button>
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                        </div>
                    </div>

                    <!-- Categories Grid -->
                    <div class="categories-grid">
                        <div class="category-card">
                            <div class="category-icon" style="background: linear-gradient(135deg, #6a3de8, #8a5cf5);">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="category-name">Figures</div>
                            <div class="category-description">Action figures, collectibles, and character models</div>
                            <div class="category-stats">
                                <div class="stat-item">
                                    <div class="stat-number">24</div>
                                    <div class="stat-label">Products</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">$2,450</div>
                                    <div class="stat-label">Revenue</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">156</div>
                                    <div class="stat-label">Sales</div>
                                </div>
                            </div>
                            <div class="category-actions">
                                <button class="btn-edit" data-category="figures"><i class="fas fa-edit"></i> Edit</button>
                                <button class="btn-delete" data-category="figures"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>

                        <div class="category-card">
                            <div class="category-icon" style="background: linear-gradient(135deg, #ff6b6b, #ff8585);">
                                <i class="fas fa-tshirt"></i>
                            </div>
                            <div class="category-name">Clothing</div>
                            <div class="category-description">T-shirts, hoodies, cosplay outfits, and accessories</div>
                            <div class="category-stats">
                                <div class="stat-item">
                                    <div class="stat-number">18</div>
                                    <div class="stat-label">Products</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">$1,890</div>
                                    <div class="stat-label">Revenue</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">89</div>
                                    <div class="stat-label">Sales</div>
                                </div>
                            </div>
                            <div class="category-actions">
                                <button class="btn-edit" data-category="clothing"><i class="fas fa-edit"></i> Edit</button>
                                <button class="btn-delete" data-category="clothing"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>

                        <div class="category-card">
                            <div class="category-icon" style="background: linear-gradient(135deg, #ffbe0b, #ffd24c);">
                                <i class="fas fa-gem"></i>
                            </div>
                            <div class="category-name">Accessories</div>
                            <div class="category-description">Keychains, bags, posters, and collectible items</div>
                            <div class="category-stats">
                                <div class="stat-item">
                                    <div class="stat-number">32</div>
                                    <div class="stat-label">Products</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">$1,250</div>
                                    <div class="stat-label">Revenue</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">203</div>
                                    <div class="stat-label">Sales</div>
                                </div>
                            </div>
                            <div class="category-actions">
                                <button class="btn-edit" data-category="accessories"><i class="fas fa-edit"></i> Edit</button>
                                <button class="btn-delete" data-category="accessories"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>

                        <div class="category-card">
                            <div class="category-icon" style="background: linear-gradient(135deg, #48c774, #5dd68d);">
                                <i class="fas fa-book"></i>
                            </div>
                            <div class="category-name">Manga</div>
                            <div class="category-description">Manga volumes, light novels, and digital content</div>
                            <div class="category-stats">
                                <div class="stat-item">
                                    <div class="stat-number">45</div>
                                    <div class="stat-label">Products</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">$3,120</div>
                                    <div class="stat-label">Revenue</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">267</div>
                                    <div class="stat-label">Sales</div>
                                </div>
                            </div>
                            <div class="category-actions">
                                <button class="btn-edit" data-category="manga"><i class="fas fa-edit"></i> Edit</button>
                                <button class="btn-delete" data-category="manga"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add/Edit Category Modal -->
        <div class="modal" id="category-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Add New Category</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <form id="category-form">
                        <div class="form-group">
                            <label for="category-name">Category Name</label>
                            <input type="text" id="category-name" required>
                        </div>
                        <div class="form-group">
                            <label for="category-description">Description</label>
                            <textarea id="category-description" placeholder="Enter category description" required></textarea>
                        </div>
                        <div class="form-group">
                            <label for="category-icon">Icon</label>
                            <select id="category-icon" required>
                                <option value="">Select Icon</option>
                                <option value="fas fa-robot">Robot (Figures)</option>
                                <option value="fas fa-tshirt">T-Shirt (Clothing)</option>
                                <option value="fas fa-gem">Gem (Accessories)</option>
                                <option value="fas fa-book">Book (Manga)</option>
                                <option value="fas fa-gamepad">Gamepad (Gaming)</option>
                                <option value="fas fa-music">Music (Audio)</option>
                                <option value="fas fa-film">Film (Movies)</option>
                                <option value="fas fa-star">Star (Featured)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Color Theme</label>
                            <div class="color-picker-group">
                                <div class="color-option" data-color="linear-gradient(135deg, #6a3de8, #8a5cf5)" style="background: linear-gradient(135deg, #6a3de8, #8a5cf5);"></div>
                                <div class="color-option" data-color="linear-gradient(135deg, #ff6b6b, #ff8585)" style="background: linear-gradient(135deg, #ff6b6b, #ff8585);"></div>
                                <div class="color-option" data-color="linear-gradient(135deg, #ffbe0b, #ffd24c)" style="background: linear-gradient(135deg, #ffbe0b, #ffd24c);"></div>
                                <div class="color-option" data-color="linear-gradient(135deg, #48c774, #5dd68d)" style="background: linear-gradient(135deg, #48c774, #5dd68d);"></div>
                                <div class="color-option" data-color="linear-gradient(135deg, #3273dc, #5a9cfc)" style="background: linear-gradient(135deg, #3273dc, #5a9cfc);"></div>
                                <div class="color-option" data-color="linear-gradient(135deg, #ff3860, #ff6b8a)" style="background: linear-gradient(135deg, #ff3860, #ff6b8a);"></div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small" form="category-form">Save Category</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Category Modal Functionality
            const addCategoryBtn = document.querySelector('.add-category-btn');
            const categoryModal = document.getElementById('category-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            const categoryForm = document.getElementById('category-form');

            // Color picker functionality
            const colorOptions = document.querySelectorAll('.color-option');
            let selectedColor = '';

            colorOptions.forEach(option => {
                option.addEventListener('click', function() {
                    colorOptions.forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedColor = this.getAttribute('data-color');
                });
            });

            // Open modal when Add Category button is clicked
            addCategoryBtn.addEventListener('click', function() {
                document.querySelector('.modal-header h2').textContent = 'Add New Category';
                categoryForm.reset();
                colorOptions.forEach(opt => opt.classList.remove('selected'));
                selectedColor = '';
                categoryModal.classList.add('active');
            });

            // Close modal functionality
            modalClose.addEventListener('click', function() {
                categoryModal.classList.remove('active');
            });

            cancelBtn.addEventListener('click', function() {
                categoryModal.classList.remove('active');
            });

            categoryModal.addEventListener('click', function(e) {
                if (e.target === categoryModal) {
                    categoryModal.classList.remove('active');
                }
            });

            // Form submission
            categoryForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const name = document.getElementById('category-name').value;
                const description = document.getElementById('category-description').value;
                const icon = document.getElementById('category-icon').value;

                if (!selectedColor) {
                    alert('Please select a color theme');
                    return;
                }

                // Create new category card
                const categoriesGrid = document.querySelector('.categories-grid');
                const newCard = document.createElement('div');
                newCard.className = 'category-card';
                newCard.innerHTML = `
                    <div class="category-icon" style="background: ${selectedColor};">
                        <i class="${icon}"></i>
                    </div>
                    <div class="category-name">${name}</div>
                    <div class="category-description">${description}</div>
                    <div class="category-stats">
                        <div class="stat-item">
                            <div class="stat-number">0</div>
                            <div class="stat-label">Products</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">$0</div>
                            <div class="stat-label">Revenue</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">0</div>
                            <div class="stat-label">Sales</div>
                        </div>
                    </div>
                    <div class="category-actions">
                        <button class="btn-edit" data-category="${name.toLowerCase()}"><i class="fas fa-edit"></i> Edit</button>
                        <button class="btn-delete" data-category="${name.toLowerCase()}"><i class="fas fa-trash"></i> Delete</button>
                    </div>
                `;

                categoriesGrid.appendChild(newCard);

                // Add event listeners to new buttons
                attachCategoryEventListeners(newCard);

                alert('Category added successfully!');
                categoryModal.classList.remove('active');
            });

            // Edit category functionality
            function attachCategoryEventListeners(card) {
                const editBtn = card.querySelector('.btn-edit');
                const deleteBtn = card.querySelector('.btn-delete');

                editBtn.addEventListener('click', function() {
                    const categoryName = card.querySelector('.category-name').textContent;
                    const categoryDescription = card.querySelector('.category-description').textContent;

                    document.querySelector('.modal-header h2').textContent = 'Edit Category';
                    document.getElementById('category-name').value = categoryName;
                    document.getElementById('category-description').value = categoryDescription;

                    categoryModal.classList.add('active');
                });

                deleteBtn.addEventListener('click', function() {
                    const categoryName = card.querySelector('.category-name').textContent;
                    if (confirm(`Are you sure you want to delete the "${categoryName}" category?`)) {
                        card.remove();
                        alert(`${categoryName} category has been deleted.`);
                    }
                });
            }

            // Attach event listeners to existing category cards
            document.querySelectorAll('.category-card').forEach(card => {
                attachCategoryEventListeners(card);
            });

            // Mobile menu toggle
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');

            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
