document.addEventListener('DOMContentLoaded', function() {
    // Theme Toggle Functionality (reusing from script.js)
    const themeToggle = document.querySelector('.theme-toggle');
    const body = document.body;
    const themeIcon = document.querySelector('.theme-toggle i');
    
    // Check for saved theme preference or use default
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        body.className = savedTheme;
        updateThemeIcon();
    }
    
    // Toggle theme when clicked
    themeToggle.addEventListener('click', () => {
        if (body.classList.contains('dark-mode')) {
            body.classList.remove('dark-mode');
            localStorage.setItem('theme', '');
        } else {
            body.classList.add('dark-mode');
            localStorage.setItem('theme', 'dark-mode');
        }
        updateThemeIcon();
    });
    
    // Update theme icon based on current theme
    function updateThemeIcon() {
        if (body.classList.contains('dark-mode')) {
            themeIcon.className = 'fas fa-sun';
        } else {
            themeIcon.className = 'fas fa-moon';
        }
    }
    
    // Mobile Menu Toggle
    const mobileMenuBtn = document.querySelector('.mobile-menu');
    const navLinks = document.querySelector('.nav-links');
    
    mobileMenuBtn.addEventListener('click', () => {
        navLinks.classList.toggle('active');
    });
    
    // Admin Panel Specific Functionality
    const adminNavLinks = document.querySelectorAll('.admin-nav a');
    const adminSections = document.querySelectorAll('.admin-section');

    // Only manage sections if we're on the main admin page (not dedicated pages)
    const isMainAdminPage = window.location.pathname.includes('admin.html') ||
                           window.location.pathname.endsWith('/admin.html') ||
                           (window.location.pathname.endsWith('/') && document.getElementById('dashboard-section'));

    if (isMainAdminPage) {
        // Initially hide all sections except dashboard
        hideAllSections();
        const dashboardSection = document.getElementById('dashboard-section');
        if (dashboardSection) {
            dashboardSection.style.display = 'block';
        }
    }
    
    // Add click event to all nav links (only on main admin page)
    if (isMainAdminPage) {
        adminNavLinks.forEach(link => {
            if (!link.getAttribute('href').startsWith('http') &&
                !link.getAttribute('href').includes('.html')) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all links
                    adminNavLinks.forEach(l => l.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Show corresponding section
                    const sectionId = this.getAttribute('data-section') + '-section';
                    hideAllSections();

                    const section = document.getElementById(sectionId);
                    if (section) {
                        section.style.display = 'block';
                    } else {
                        // If section doesn't exist yet, show a message
                        createComingSoonSection(this.getAttribute('data-section'));
                    }

                    // On mobile, hide sidebar after selection
                    if (window.innerWidth <= 768) {
                        document.querySelector('.admin-sidebar').classList.remove('active');
                    }
                });
            }
        });
    }
    
    // Mobile sidebar toggle
    const mobileMenuToggle = document.querySelector('.mobile-menu');
    if (mobileMenuToggle) {
        mobileMenuToggle.addEventListener('click', function() {
            document.querySelector('.admin-sidebar').classList.toggle('active');
        });
    }
    
    // Helper function to hide all sections (only on main admin page)
    function hideAllSections() {
        if (isMainAdminPage) {
            adminSections.forEach(section => {
                section.style.display = 'none';
            });

            // Also remove any coming soon sections
            const comingSoonSection = document.querySelector('.coming-soon-section');
            if (comingSoonSection) {
                comingSoonSection.remove();
            }
        }
    }
    
    // Helper function to create a coming soon section
    function createComingSoonSection(sectionName) {
        const adminContent = document.querySelector('.admin-content');
        
        const comingSoonSection = document.createElement('div');
        comingSoonSection.className = 'admin-section coming-soon-section';
        comingSoonSection.style.display = 'block';
        
        // Format the section name for display (capitalize first letter)
        const formattedName = sectionName.charAt(0).toUpperCase() + sectionName.slice(1);
        
        comingSoonSection.innerHTML = `
            <div class="admin-header">
                <h1>${formattedName}</h1>
            </div>
            <div class="coming-soon-content" style="text-align: center; padding: 50px 0;">
                <i class="fas fa-tools" style="font-size: 4rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                <h2>Coming Soon</h2>
                <p>The ${formattedName} section is under development and will be available soon.</p>
            </div>
        `;
        
        adminContent.appendChild(comingSoonSection);
    }
    
    // Initialize charts (placeholder for now)
    initCharts();
    
    function initCharts() {
        // This would be replaced with actual chart initialization code
        console.log('Charts would be initialized here with a library like Chart.js');
    }
    
    // Add product functionality (placeholder)
    const addProductBtn = document.querySelector('.add-product-btn');
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            alert('Add product functionality would be implemented here');
        });
    }
    
    // Order status update functionality (placeholder)
    const statusUpdateBtns = document.querySelectorAll('.status-update');
    statusUpdateBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const orderId = this.getAttribute('data-order-id');
            const status = this.getAttribute('data-status');
            alert(`Order ${orderId} status would be updated to ${status}`);
        });
    });
});