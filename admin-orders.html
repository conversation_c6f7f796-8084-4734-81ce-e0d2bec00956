<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles (Same as admin.html) */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        /* Sidebar Styles */
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        /* Main Content Styles */
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Orders Page Specific Styles */
        .orders-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select,
        .search-box input {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-box button {
            padding: 8px 12px;
            border: none;
            background-color: var(--primary-color);
            color: white;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }

        .orders-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .orders-table th,
        .orders-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .orders-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }

        .orders-table tr:last-child td {
            border-bottom: none;
        }

        .order-id {
            font-weight: 600;
            color: var(--primary-color);
        }

        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            text-align: center;
            display: inline-block;
        }

        .status.completed {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }

        .status.processing {
            background-color: rgba(255, 190, 11, 0.2);
            color: var(--accent-color);
        }

        .status.cancelled {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }

        .status.shipped {
            background-color: rgba(106, 61, 232, 0.2);
            color: var(--primary-color);
        }

        .order-actions {
            display: flex;
            gap: 10px;
        }

        .order-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .order-actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination button {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--card-bg);
            color: var(--text-color);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination button:hover:not(.active) {
            background-color: var(--bg-color);
        }

        /* Order Details Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            padding: 50px 0;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: 10px;
            max-width: 800px;
            margin: 0 auto;
            box-shadow: 0 5px 20px var(--shadow-color);
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            opacity: 1;
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .order-details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .order-detail-block h3 {
            font-size: 1.1rem;
            margin-bottom: 10px;
            color: var(--primary-color);
        }

        .order-detail-block p {
            margin-bottom: 5px;
        }

        .order-items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .order-items-table th,
        .order-items-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .order-items-table th {
            font-weight: 600;
        }

        .order-items-table tr:last-child td {
            border-bottom: none;
        }

        .order-item-image {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            object-fit: cover;
        }

        .order-total {
            text-align: right;
            font-size: 1.1rem;
            font-weight: 700;
            margin-top: 20px;
        }

        .status-update-form {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .status-update-form h3 {
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .status-update-form .form-group {
            margin-bottom: 15px;
        }

        .status-update-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .status-update-form select {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .status-update-form textarea {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            min-height: 100px;
            resize: vertical;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-cancel {
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Responsive Styles */
        @media (max-width: 992px) {
            .order-details-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }
            
            .orders-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .orders-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>
                
                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html" class="active"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="#" data-section="categories"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="#" data-section="inventory"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="#" data-section="analytics"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="#" data-section="settings"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>
            
            <!-- Admin Content -->
            <div class="admin-content">
                <!-- Orders Section -->
                <div class="admin-section" id="orders-section">
                    <div class="admin-header">
                        <h1>Orders</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                            <button class="btn btn-small"><i class="fas fa-sync-alt"></i> Refresh</button>
                        </div>
                    </div>
                    
                    <!-- Orders Filters -->
                    <div class="orders-filters">
                        <div class="filter-group">
                            <label for="status-filter">Status:</label>
                            <select id="status-filter">
                                <option value="all">All Statuses</option>
                                <option value="processing">Processing</option>
                                <option value="shipped">Shipped</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        
                        <div class="filter-group">
                            <label for="date-filter">Date Range:</label>
                            <select id="date-filter">
                                <option value="all">All Time</option>
                                <option value="today">Today</option>
                                <option value="week">This Week</option>
                                <option value="month">This Month</option>
                                <option value="year">This Year</option>
                            </select>
                        </div>
                        
                        <div class="search-box">
                            <input type="text" placeholder="Search orders...">
                            <button><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                    
                    <!-- Orders Table -->
                    <table class="orders-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Date</th>
                                <th>Items</th>
                                <th>Total</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Order Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="order-details-grid">
                        <div class="order-detail-block">
                            <h3>Order Information</h3>
                            <p><strong>Order ID:</strong> <span id="modal-order-id">#INF12345</span></p>
                            <p><strong>Date:</strong> <span id="modal-order-date">June 15, 2023</span></p>
                            <p><strong>Payment Method:</strong> <span id="modal-payment-method">Credit Card (•••• 1234)</span></p>
                            <p><strong>Status:</strong> <span id="modal-order-status" class="status completed">Completed</span></p>
                        </div>
                        <div class="order-detail-block">
                            <h3>Customer Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                        </div>
                    </div>
                    
                    <div class="order-detail-block">
                        <h3>Shipping Address</h3>
                        <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                    </div>
                    
                    <h3>Order Items</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modal-order-items">
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/1.jpg" alt="Product" class="order-item-image">
                                        <span>Naruto Uzumaki Figure</span>
                                    </div>
                                </td>
                                <td>$59.99</td>
                                <td>1</td>
                                <td>$59.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/2.jpg" alt="Product" class="order-item-image">
                                        <span>Attack on Titan Hoodie</span>
                                    </div>
                                </td>
                                <td>$39.99</td>
                                <td>1</td>
                                <td>$39.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/3.jpg" alt="Product" class="order-item-image">
                                        <span>My Hero Academia Backpack</span>
                                    </div>
                                </td>
                                <td>$45.99</td>
                                <td>1</td>
                                <td>$45.99</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="order-total">
                        <p><strong>Subtotal:</strong> $145.97</p>
                        <p><strong>Shipping:</strong> $0.00</p>
                        <p><strong>Tax:</strong> $14.60</p>
                        <p><strong>Total:</strong> $160.57</p>
                    </div>
                    
                    <div class="status-update-form">
                        <h3>Update Order Status</h3>
                        <form id="status-update-form">
                            <div class="form-group">
                                <label for="order-status">Status</label>
                                <select id="order-status">
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-notes">Notes</label>
                                <textarea id="status-notes" placeholder="Add notes about this status update"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small status-update" data-order-id="INF12345">Update Status</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Order Modal Functionality
            const viewOrderBtns = document.querySelectorAll('.view-order');
            const orderModal = document.getElementById('order-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Sample order data for demo purposes
            const orderData = {
                'INF12345': {
                    id: 'INF12345',
                    customer: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 15, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 1234)',
                    address: '123 Anime Street, Tokyo, JP 100-0001',
                    items: [
                        {name: 'Naruto Uzumaki Figure', image: 'images/1.jpg', price: 59.99, qty: 1, total: 59.99},
                        {name: 'Attack on Titan Hoodie', image: 'images/2.jpg', price: 39.99, qty: 1, total: 39.99},
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 145.97,
                    shipping: 0,
                    tax: 14.60,
                    total: 160.57
                },
                'INF12346': {
                    id: 'INF12346',
                    customer: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'processing',
                    payment: 'PayPal',
                    address: '456 Manga Road, Kyoto, JP 600-8216',
                    items: [
                        {name: 'One Piece Luffy Figure', image: 'images/4.jpg', price: 49.99, qty: 1, total: 49.99},
                        {name: 'Dragon Ball Z T-Shirt', image: 'images/2.jpg', price: 39.51, qty: 1, total: 39.51}
                    ],
                    subtotal: 89.50,
                    shipping: 0,
                    tax: 8.95,
                    total: 98.45
                },
                'INF12347': {
                    id: 'INF12347',
                    customer: 'Robert Johnson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'cancelled',
                    payment: 'Credit Card (•••• 5678)',
                    address: '789 Anime Avenue, Osaka, JP 530-0001',
                    items: [
                        {name: 'Demon Slayer Complete Manga Set', image: 'images/3.jpg', price: 120.00, qty: 1, total: 120.00},
                        {name: 'Anime Poster Collection', image: 'images/7.jpg', price: 22.95, qty: 3, total: 68.85},
                        {name: 'Anime Sticker Pack', image: 'images/1.jpg', price: 7.99, qty: 1, total: 7.99},
                        {name: 'Anime Keychain', image: 'images/2.jpg', price: 6.99, qty: 2, total: 13.98}
                    ],
                    subtotal: 210.82,
                    shipping: 0,
                    tax: 21.08,
                    total: 231.90
                },
                'INF12348': {
                    id: 'INF12348',
                    customer: 'Emily Davis',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 9012)',
                    address: '101 Otaku Lane, Tokyo, JP 100-0002',
                    items: [
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 45.99,
                    shipping: 0,
                    tax: 4.60,
                    total: 50.59
                },
                'INF12349': {
                    id: 'INF12349',
                    customer: 'Michael Wilson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'shipped',
                    payment: 'Credit Card (•••• 3456)',
                    address: '202 Manga Street, Kyoto, JP 600-8217',
                    items: [
                        {name: 'Attack on Titan Figure Set', image: 'images/1.jpg', price: 89.99, qty: 1, total: 89.99},
                        {name: 'Anime Themed Mug', image: 'images/2.jpg', price: 15.99, qty: 2, total: 31.98},
                        {name: 'Naruto Headband', image: 'images/3.jpg', price: 19.99, qty: 1, total: 19.99},
                        {name: 'Anime Wall Scroll', image: 'images/4.jpg', price: 33.29, qty: 1, total: 33.29}
                    ],
                    subtotal: 175.25,
                    shipping: 0,
                    tax: 17.53,
                    total: 192.78
                }
            };
            
            // Open modal when View Order button is clicked
            viewOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    const order = orderData[orderId];
                    
                    if (order) {
                        // Update modal with order details
                        document.getElementById('modal-order-id').textContent = '#' + order.id;
                        document.getElementById('modal-order-date').textContent = order.date;
                        document.getElementById('modal-payment-method').textContent = order.payment;
                        
                        const statusElement = document.getElementById('modal-order-status');
                        statusElement.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
                        statusElement.className = 'status ' + order.status;
                        
                        document.getElementById('modal-customer-name').textContent = order.customer;
                        document.getElementById('modal-customer-email').textContent = order.email;
                        document.getElementById('modal-customer-phone').textContent = order.phone;
                        document.getElementById('modal-shipping-address').textContent = order.address;
                        
                        // Update order items
                        const orderItemsContainer = document.getElementById('modal-order-items');
                        orderItemsContainer.innerHTML = '';
                        
                        order.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="${item.image}" alt="Product" class="order-item-image">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>${item.qty}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                        
                        // Update order totals
                        const orderTotal = document.querySelector('.order-total');
                        orderTotal.innerHTML = `
                            <p><strong>Subtotal:</strong> $${order.subtotal.toFixed(2)}</p>
                            <p><strong>Shipping:</strong> $${order.shipping.toFixed(2)}</p>
                            <p><strong>Tax:</strong> $${order.tax.toFixed(2)}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                        `;
                        
                        // Update status update button
                        document.querySelector('.status-update').setAttribute('data-order-id', order.id);
                        
                        // Set current status in dropdown
                        const statusSelect = document.getElementById('order-status');
                        for (let i = 0; i < statusSelect.options.length; i++) {
                            if (statusSelect.options[i].value === order.status) {
                                statusSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        // Clear notes
                        document.getElementById('status-notes').value = '';
                    }
                    
                    orderModal.classList.add('active');
                });
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            orderModal.addEventListener('click', function(e) {
                if (e.target === orderModal) {
                    orderModal.classList.remove('active');
                }
            });
            
            // Print order functionality (placeholder)
            const printOrderBtns = document.querySelectorAll('.print-order');
            
            printOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    alert(`Printing order #${orderId}...`);
                    // In a real application, this would trigger a print function
                });
            });
            
            // Status update functionality (placeholder)
            const statusUpdateBtn = document.querySelector('.status-update');
            
            statusUpdateBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const status = document.getElementById('order-status').value;
                const notes = document.getElementById('status-notes').value;
                
                alert(`Order #${orderId} status updated to ${status}${notes ? ' with notes' : ''}`);
                orderModal.classList.remove('active');
                
                // Update status in the table
                const orderRow = document.querySelector(`[data-id="${orderId}"]`).closest('tr');
                const statusCell = orderRow.querySelector('.status');
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'status ' + status;
            });
            
            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');
            
            function filterOrders() {
                const statusValue = statusFilter.value;
                const dateValue = dateFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                const rows = document.querySelectorAll('.orders-table tbody tr');
                
                rows.forEach(row => {
                    const orderStatus = row.querySelector('.status').textContent.toLowerCase();
                    const orderDate = row.querySelectorAll('td')[2].textContent; // Date column
                    const orderCustomer = row.querySelectorAll('td')[1].textContent.toLowerCase(); // Customer column
                    const orderId = row.querySelector('.order-id').textContent.toLowerCase();
                    
                    let showByStatus = statusValue === 'all' || orderStatus === statusValue;
                    let showByDate = true; // Default to true
                    
                    // Simple date filtering logic (could be more sophisticated in a real app)
                    if (dateValue !== 'all') {
                        const today = new Date();
                        const orderDateObj = new Date(orderDate);
                        
                        if (dateValue === 'today') {
                            showByDate = orderDateObj.toDateString() === today.toDateString();
                        } else if (dateValue === 'week') {
                            const weekAgo = new Date(today.setDate(today.getDate() - 7));
                            showByDate = orderDateObj >= weekAgo;
                        } else if (dateValue === 'month') {
                            const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
                            showByDate = orderDateObj >= monthAgo;
                        } else if (dateValue === 'year') {
                            const yearAgo = new Date(today.setFullYear(today.getFullYear() - 1));
                            showByDate = orderDateObj >= yearAgo;
                        }
                    }
                    
                    let showBySearch = searchValue === '' || 
                                      orderCustomer.includes(searchValue) || 
                                      orderId.includes(searchValue);
                    
                    if (showByStatus && showByDate && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
            searchButton.addEventListener('click', filterOrders);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterOrders();
                }
            });
            
            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');
            
            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
</body>
</html>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="order-id">#INF12345</td>
                                <td>John Doe</td>
                                <td>June 15, 2023</td>
                                <td>3</td>
                                <td>$160.57</td>
                                <td><span class="status completed">Completed</span></td>
                                <td class="order-actions">
                                    <button title="View" class="view-order" data-id="INF12345"><i class="fas fa-eye"></i></button>
                                    <button title="Print" class="print-order" data-id="INF12345"><i class="fas fa-print"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Order Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="order-details-grid">
                        <div class="order-detail-block">
                            <h3>Order Information</h3>
                            <p><strong>Order ID:</strong> <span id="modal-order-id">#INF12345</span></p>
                            <p><strong>Date:</strong> <span id="modal-order-date">June 15, 2023</span></p>
                            <p><strong>Payment Method:</strong> <span id="modal-payment-method">Credit Card (•••• 1234)</span></p>
                            <p><strong>Status:</strong> <span id="modal-order-status" class="status completed">Completed</span></p>
                        </div>
                        <div class="order-detail-block">
                            <h3>Customer Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                        </div>
                    </div>
                    
                    <div class="order-detail-block">
                        <h3>Shipping Address</h3>
                        <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                    </div>
                    
                    <h3>Order Items</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modal-order-items">
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/1.jpg" alt="Product" class="order-item-image">
                                        <span>Naruto Uzumaki Figure</span>
                                    </div>
                                </td>
                                <td>$59.99</td>
                                <td>1</td>
                                <td>$59.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/2.jpg" alt="Product" class="order-item-image">
                                        <span>Attack on Titan Hoodie</span>
                                    </div>
                                </td>
                                <td>$39.99</td>
                                <td>1</td>
                                <td>$39.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/3.jpg" alt="Product" class="order-item-image">
                                        <span>My Hero Academia Backpack</span>
                                    </div>
                                </td>
                                <td>$45.99</td>
                                <td>1</td>
                                <td>$45.99</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="order-total">
                        <p><strong>Subtotal:</strong> $145.97</p>
                        <p><strong>Shipping:</strong> $0.00</p>
                        <p><strong>Tax:</strong> $14.60</p>
                        <p><strong>Total:</strong> $160.57</p>
                    </div>
                    
                    <div class="status-update-form">
                        <h3>Update Order Status</h3>
                        <form id="status-update-form">
                            <div class="form-group">
                                <label for="order-status">Status</label>
                                <select id="order-status">
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-notes">Notes</label>
                                <textarea id="status-notes" placeholder="Add notes about this status update"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small status-update" data-order-id="INF12345">Update Status</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Order Modal Functionality
            const viewOrderBtns = document.querySelectorAll('.view-order');
            const orderModal = document.getElementById('order-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Sample order data for demo purposes
            const orderData = {
                'INF12345': {
                    id: 'INF12345',
                    customer: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 15, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 1234)',
                    address: '123 Anime Street, Tokyo, JP 100-0001',
                    items: [
                        {name: 'Naruto Uzumaki Figure', image: 'images/1.jpg', price: 59.99, qty: 1, total: 59.99},
                        {name: 'Attack on Titan Hoodie', image: 'images/2.jpg', price: 39.99, qty: 1, total: 39.99},
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 145.97,
                    shipping: 0,
                    tax: 14.60,
                    total: 160.57
                },
                'INF12346': {
                    id: 'INF12346',
                    customer: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'processing',
                    payment: 'PayPal',
                    address: '456 Manga Road, Kyoto, JP 600-8216',
                    items: [
                        {name: 'One Piece Luffy Figure', image: 'images/4.jpg', price: 49.99, qty: 1, total: 49.99},
                        {name: 'Dragon Ball Z T-Shirt', image: 'images/2.jpg', price: 39.51, qty: 1, total: 39.51}
                    ],
                    subtotal: 89.50,
                    shipping: 0,
                    tax: 8.95,
                    total: 98.45
                },
                'INF12347': {
                    id: 'INF12347',
                    customer: 'Robert Johnson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'cancelled',
                    payment: 'Credit Card (•••• 5678)',
                    address: '789 Anime Avenue, Osaka, JP 530-0001',
                    items: [
                        {name: 'Demon Slayer Complete Manga Set', image: 'images/3.jpg', price: 120.00, qty: 1, total: 120.00},
                        {name: 'Anime Poster Collection', image: 'images/7.jpg', price: 22.95, qty: 3, total: 68.85},
                        {name: 'Anime Sticker Pack', image: 'images/1.jpg', price: 7.99, qty: 1, total: 7.99},
                        {name: 'Anime Keychain', image: 'images/2.jpg', price: 6.99, qty: 2, total: 13.98}
                    ],
                    subtotal: 210.82,
                    shipping: 0,
                    tax: 21.08,
                    total: 231.90
                },
                'INF12348': {
                    id: 'INF12348',
                    customer: 'Emily Davis',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 9012)',
                    address: '101 Otaku Lane, Tokyo, JP 100-0002',
                    items: [
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 45.99,
                    shipping: 0,
                    tax: 4.60,
                    total: 50.59
                },
                'INF12349': {
                    id: 'INF12349',
                    customer: 'Michael Wilson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'shipped',
                    payment: 'Credit Card (•••• 3456)',
                    address: '202 Manga Street, Kyoto, JP 600-8217',
                    items: [
                        {name: 'Attack on Titan Figure Set', image: 'images/1.jpg', price: 89.99, qty: 1, total: 89.99},
                        {name: 'Anime Themed Mug', image: 'images/2.jpg', price: 15.99, qty: 2, total: 31.98},
                        {name: 'Naruto Headband', image: 'images/3.jpg', price: 19.99, qty: 1, total: 19.99},
                        {name: 'Anime Wall Scroll', image: 'images/4.jpg', price: 33.29, qty: 1, total: 33.29}
                    ],
                    subtotal: 175.25,
                    shipping: 0,
                    tax: 17.53,
                    total: 192.78
                }
            };
            
            // Open modal when View Order button is clicked
            viewOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    const order = orderData[orderId];
                    
                    if (order) {
                        // Update modal with order details
                        document.getElementById('modal-order-id').textContent = '#' + order.id;
                        document.getElementById('modal-order-date').textContent = order.date;
                        document.getElementById('modal-payment-method').textContent = order.payment;
                        
                        const statusElement = document.getElementById('modal-order-status');
                        statusElement.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
                        statusElement.className = 'status ' + order.status;
                        
                        document.getElementById('modal-customer-name').textContent = order.customer;
                        document.getElementById('modal-customer-email').textContent = order.email;
                        document.getElementById('modal-customer-phone').textContent = order.phone;
                        document.getElementById('modal-shipping-address').textContent = order.address;
                        
                        // Update order items
                        const orderItemsContainer = document.getElementById('modal-order-items');
                        orderItemsContainer.innerHTML = '';
                        
                        order.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="${item.image}" alt="Product" class="order-item-image">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>${item.qty}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                        
                        // Update order totals
                        const orderTotal = document.querySelector('.order-total');
                        orderTotal.innerHTML = `
                            <p><strong>Subtotal:</strong> $${order.subtotal.toFixed(2)}</p>
                            <p><strong>Shipping:</strong> $${order.shipping.toFixed(2)}</p>
                            <p><strong>Tax:</strong> $${order.tax.toFixed(2)}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                        `;
                        
                        // Update status update button
                        document.querySelector('.status-update').setAttribute('data-order-id', order.id);
                        
                        // Set current status in dropdown
                        const statusSelect = document.getElementById('order-status');
                        for (let i = 0; i < statusSelect.options.length; i++) {
                            if (statusSelect.options[i].value === order.status) {
                                statusSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        // Clear notes
                        document.getElementById('status-notes').value = '';
                    }
                    
                    orderModal.classList.add('active');
                });
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            orderModal.addEventListener('click', function(e) {
                if (e.target === orderModal) {
                    orderModal.classList.remove('active');
                }
            });
            
            // Print order functionality (placeholder)
            const printOrderBtns = document.querySelectorAll('.print-order');
            
            printOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    alert(`Printing order #${orderId}...`);
                    // In a real application, this would trigger a print function
                });
            });
            
            // Status update functionality (placeholder)
            const statusUpdateBtn = document.querySelector('.status-update');
            
            statusUpdateBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const status = document.getElementById('order-status').value;
                const notes = document.getElementById('status-notes').value;
                
                alert(`Order #${orderId} status updated to ${status}${notes ? ' with notes' : ''}`);
                orderModal.classList.remove('active');
                
                // Update status in the table
                const orderRow = document.querySelector(`[data-id="${orderId}"]`).closest('tr');
                const statusCell = orderRow.querySelector('.status');
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'status ' + status;
            });
            
            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');
            
            function filterOrders() {
                const statusValue = statusFilter.value;
                const dateValue = dateFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                const rows = document.querySelectorAll('.orders-table tbody tr');
                
                rows.forEach(row => {
                    const orderStatus = row.querySelector('.status').textContent.toLowerCase();
                    const orderDate = row.querySelectorAll('td')[2].textContent; // Date column
                    const orderCustomer = row.querySelectorAll('td')[1].textContent.toLowerCase(); // Customer column
                    const orderId = row.querySelector('.order-id').textContent.toLowerCase();
                    
                    let showByStatus = statusValue === 'all' || orderStatus === statusValue;
                    let showByDate = true; // Default to true
                    
                    // Simple date filtering logic (could be more sophisticated in a real app)
                    if (dateValue !== 'all') {
                        const today = new Date();
                        const orderDateObj = new Date(orderDate);
                        
                        if (dateValue === 'today') {
                            showByDate = orderDateObj.toDateString() === today.toDateString();
                        } else if (dateValue === 'week') {
                            const weekAgo = new Date(today.setDate(today.getDate() - 7));
                            showByDate = orderDateObj >= weekAgo;
                        } else if (dateValue === 'month') {
                            const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
                            showByDate = orderDateObj >= monthAgo;
                        } else if (dateValue === 'year') {
                            const yearAgo = new Date(today.setFullYear(today.getFullYear() - 1));
                            showByDate = orderDateObj >= yearAgo;
                        }
                    }
                    
                    let showBySearch = searchValue === '' || 
                                      orderCustomer.includes(searchValue) || 
                                      orderId.includes(searchValue);
                    
                    if (showByStatus && showByDate && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
            searchButton.addEventListener('click', filterOrders);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterOrders();
                }
            });
            
            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');
            
            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
                            <tr>
                                <td class="order-id">#INF12346</td>
                                <td>Jane Smith</td>
                                <td>June 14, 2023</td>
                                <td>2</td>
                                <td>$89.50</td>
                                <td><span class="status processing">Processing</span></td>
                                <td class="order-actions">
                                    <button title="View" class="view-order" data-id="INF12346"><i class="fas fa-eye"></i></button>
                                    <button title="Print" class="print-order" data-id="INF12346"><i class="fas fa-print"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Order Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="order-details-grid">
                        <div class="order-detail-block">
                            <h3>Order Information</h3>
                            <p><strong>Order ID:</strong> <span id="modal-order-id">#INF12345</span></p>
                            <p><strong>Date:</strong> <span id="modal-order-date">June 15, 2023</span></p>
                            <p><strong>Payment Method:</strong> <span id="modal-payment-method">Credit Card (•••• 1234)</span></p>
                            <p><strong>Status:</strong> <span id="modal-order-status" class="status completed">Completed</span></p>
                        </div>
                        <div class="order-detail-block">
                            <h3>Customer Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                        </div>
                    </div>
                    
                    <div class="order-detail-block">
                        <h3>Shipping Address</h3>
                        <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                    </div>
                    
                    <h3>Order Items</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modal-order-items">
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/1.jpg" alt="Product" class="order-item-image">
                                        <span>Naruto Uzumaki Figure</span>
                                    </div>
                                </td>
                                <td>$59.99</td>
                                <td>1</td>
                                <td>$59.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/2.jpg" alt="Product" class="order-item-image">
                                        <span>Attack on Titan Hoodie</span>
                                    </div>
                                </td>
                                <td>$39.99</td>
                                <td>1</td>
                                <td>$39.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/3.jpg" alt="Product" class="order-item-image">
                                        <span>My Hero Academia Backpack</span>
                                    </div>
                                </td>
                                <td>$45.99</td>
                                <td>1</td>
                                <td>$45.99</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="order-total">
                        <p><strong>Subtotal:</strong> $145.97</p>
                        <p><strong>Shipping:</strong> $0.00</p>
                        <p><strong>Tax:</strong> $14.60</p>
                        <p><strong>Total:</strong> $160.57</p>
                    </div>
                    
                    <div class="status-update-form">
                        <h3>Update Order Status</h3>
                        <form id="status-update-form">
                            <div class="form-group">
                                <label for="order-status">Status</label>
                                <select id="order-status">
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-notes">Notes</label>
                                <textarea id="status-notes" placeholder="Add notes about this status update"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small status-update" data-order-id="INF12345">Update Status</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Order Modal Functionality
            const viewOrderBtns = document.querySelectorAll('.view-order');
            const orderModal = document.getElementById('order-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Sample order data for demo purposes
            const orderData = {
                'INF12345': {
                    id: 'INF12345',
                    customer: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 15, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 1234)',
                    address: '123 Anime Street, Tokyo, JP 100-0001',
                    items: [
                        {name: 'Naruto Uzumaki Figure', image: 'images/1.jpg', price: 59.99, qty: 1, total: 59.99},
                        {name: 'Attack on Titan Hoodie', image: 'images/2.jpg', price: 39.99, qty: 1, total: 39.99},
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 145.97,
                    shipping: 0,
                    tax: 14.60,
                    total: 160.57
                },
                'INF12346': {
                    id: 'INF12346',
                    customer: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'processing',
                    payment: 'PayPal',
                    address: '456 Manga Road, Kyoto, JP 600-8216',
                    items: [
                        {name: 'One Piece Luffy Figure', image: 'images/4.jpg', price: 49.99, qty: 1, total: 49.99},
                        {name: 'Dragon Ball Z T-Shirt', image: 'images/2.jpg', price: 39.51, qty: 1, total: 39.51}
                    ],
                    subtotal: 89.50,
                    shipping: 0,
                    tax: 8.95,
                    total: 98.45
                },
                'INF12347': {
                    id: 'INF12347',
                    customer: 'Robert Johnson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'cancelled',
                    payment: 'Credit Card (•••• 5678)',
                    address: '789 Anime Avenue, Osaka, JP 530-0001',
                    items: [
                        {name: 'Demon Slayer Complete Manga Set', image: 'images/3.jpg', price: 120.00, qty: 1, total: 120.00},
                        {name: 'Anime Poster Collection', image: 'images/7.jpg', price: 22.95, qty: 3, total: 68.85},
                        {name: 'Anime Sticker Pack', image: 'images/1.jpg', price: 7.99, qty: 1, total: 7.99},
                        {name: 'Anime Keychain', image: 'images/2.jpg', price: 6.99, qty: 2, total: 13.98}
                    ],
                    subtotal: 210.82,
                    shipping: 0,
                    tax: 21.08,
                    total: 231.90
                },
                'INF12348': {
                    id: 'INF12348',
                    customer: 'Emily Davis',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 9012)',
                    address: '101 Otaku Lane, Tokyo, JP 100-0002',
                    items: [
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 45.99,
                    shipping: 0,
                    tax: 4.60,
                    total: 50.59
                },
                'INF12349': {
                    id: 'INF12349',
                    customer: 'Michael Wilson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'shipped',
                    payment: 'Credit Card (•••• 3456)',
                    address: '202 Manga Street, Kyoto, JP 600-8217',
                    items: [
                        {name: 'Attack on Titan Figure Set', image: 'images/1.jpg', price: 89.99, qty: 1, total: 89.99},
                        {name: 'Anime Themed Mug', image: 'images/2.jpg', price: 15.99, qty: 2, total: 31.98},
                        {name: 'Naruto Headband', image: 'images/3.jpg', price: 19.99, qty: 1, total: 19.99},
                        {name: 'Anime Wall Scroll', image: 'images/4.jpg', price: 33.29, qty: 1, total: 33.29}
                    ],
                    subtotal: 175.25,
                    shipping: 0,
                    tax: 17.53,
                    total: 192.78
                }
            };
            
            // Open modal when View Order button is clicked
            viewOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    const order = orderData[orderId];
                    
                    if (order) {
                        // Update modal with order details
                        document.getElementById('modal-order-id').textContent = '#' + order.id;
                        document.getElementById('modal-order-date').textContent = order.date;
                        document.getElementById('modal-payment-method').textContent = order.payment;
                        
                        const statusElement = document.getElementById('modal-order-status');
                        statusElement.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
                        statusElement.className = 'status ' + order.status;
                        
                        document.getElementById('modal-customer-name').textContent = order.customer;
                        document.getElementById('modal-customer-email').textContent = order.email;
                        document.getElementById('modal-customer-phone').textContent = order.phone;
                        document.getElementById('modal-shipping-address').textContent = order.address;
                        
                        // Update order items
                        const orderItemsContainer = document.getElementById('modal-order-items');
                        orderItemsContainer.innerHTML = '';
                        
                        order.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="${item.image}" alt="Product" class="order-item-image">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>${item.qty}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                        
                        // Update order totals
                        const orderTotal = document.querySelector('.order-total');
                        orderTotal.innerHTML = `
                            <p><strong>Subtotal:</strong> $${order.subtotal.toFixed(2)}</p>
                            <p><strong>Shipping:</strong> $${order.shipping.toFixed(2)}</p>
                            <p><strong>Tax:</strong> $${order.tax.toFixed(2)}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                        `;
                        
                        // Update status update button
                        document.querySelector('.status-update').setAttribute('data-order-id', order.id);
                        
                        // Set current status in dropdown
                        const statusSelect = document.getElementById('order-status');
                        for (let i = 0; i < statusSelect.options.length; i++) {
                            if (statusSelect.options[i].value === order.status) {
                                statusSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        // Clear notes
                        document.getElementById('status-notes').value = '';
                    }
                    
                    orderModal.classList.add('active');
                });
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            orderModal.addEventListener('click', function(e) {
                if (e.target === orderModal) {
                    orderModal.classList.remove('active');
                }
            });
            
            // Print order functionality (placeholder)
            const printOrderBtns = document.querySelectorAll('.print-order');
            
            printOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    alert(`Printing order #${orderId}...`);
                    // In a real application, this would trigger a print function
                });
            });
            
            // Status update functionality (placeholder)
            const statusUpdateBtn = document.querySelector('.status-update');
            
            statusUpdateBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const status = document.getElementById('order-status').value;
                const notes = document.getElementById('status-notes').value;
                
                alert(`Order #${orderId} status updated to ${status}${notes ? ' with notes' : ''}`);
                orderModal.classList.remove('active');
                
                // Update status in the table
                const orderRow = document.querySelector(`[data-id="${orderId}"]`).closest('tr');
                const statusCell = orderRow.querySelector('.status');
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'status ' + status;
            });
            
            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');
            
            function filterOrders() {
                const statusValue = statusFilter.value;
                const dateValue = dateFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                const rows = document.querySelectorAll('.orders-table tbody tr');
                
                rows.forEach(row => {
                    const orderStatus = row.querySelector('.status').textContent.toLowerCase();
                    const orderDate = row.querySelectorAll('td')[2].textContent; // Date column
                    const orderCustomer = row.querySelectorAll('td')[1].textContent.toLowerCase(); // Customer column
                    const orderId = row.querySelector('.order-id').textContent.toLowerCase();
                    
                    let showByStatus = statusValue === 'all' || orderStatus === statusValue;
                    let showByDate = true; // Default to true
                    
                    // Simple date filtering logic (could be more sophisticated in a real app)
                    if (dateValue !== 'all') {
                        const today = new Date();
                        const orderDateObj = new Date(orderDate);
                        
                        if (dateValue === 'today') {
                            showByDate = orderDateObj.toDateString() === today.toDateString();
                        } else if (dateValue === 'week') {
                            const weekAgo = new Date(today.setDate(today.getDate() - 7));
                            showByDate = orderDateObj >= weekAgo;
                        } else if (dateValue === 'month') {
                            const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
                            showByDate = orderDateObj >= monthAgo;
                        } else if (dateValue === 'year') {
                            const yearAgo = new Date(today.setFullYear(today.getFullYear() - 1));
                            showByDate = orderDateObj >= yearAgo;
                        }
                    }
                    
                    let showBySearch = searchValue === '' || 
                                      orderCustomer.includes(searchValue) || 
                                      orderId.includes(searchValue);
                    
                    if (showByStatus && showByDate && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
            searchButton.addEventListener('click', filterOrders);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterOrders();
                }
            });
            
            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');
            
            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
                            <tr>
                                <td class="order-id">#INF12347</td>
                                <td>Robert Johnson</td>
                                <td>June 14, 2023</td>
                                <td>5</td>
                                <td>$210.75</td>
                                <td><span class="status cancelled">Cancelled</span></td>
                                <td class="order-actions">
                                    <button title="View" class="view-order" data-id="INF12347"><i class="fas fa-eye"></i></button>
                                    <button title="Print" class="print-order" data-id="INF12347"><i class="fas fa-print"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Order Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="order-details-grid">
                        <div class="order-detail-block">
                            <h3>Order Information</h3>
                            <p><strong>Order ID:</strong> <span id="modal-order-id">#INF12345</span></p>
                            <p><strong>Date:</strong> <span id="modal-order-date">June 15, 2023</span></p>
                            <p><strong>Payment Method:</strong> <span id="modal-payment-method">Credit Card (•••• 1234)</span></p>
                            <p><strong>Status:</strong> <span id="modal-order-status" class="status completed">Completed</span></p>
                        </div>
                        <div class="order-detail-block">
                            <h3>Customer Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                        </div>
                    </div>
                    
                    <div class="order-detail-block">
                        <h3>Shipping Address</h3>
                        <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                    </div>
                    
                    <h3>Order Items</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modal-order-items">
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/1.jpg" alt="Product" class="order-item-image">
                                        <span>Naruto Uzumaki Figure</span>
                                    </div>
                                </td>
                                <td>$59.99</td>
                                <td>1</td>
                                <td>$59.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/2.jpg" alt="Product" class="order-item-image">
                                        <span>Attack on Titan Hoodie</span>
                                    </div>
                                </td>
                                <td>$39.99</td>
                                <td>1</td>
                                <td>$39.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/3.jpg" alt="Product" class="order-item-image">
                                        <span>My Hero Academia Backpack</span>
                                    </div>
                                </td>
                                <td>$45.99</td>
                                <td>1</td>
                                <td>$45.99</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="order-total">
                        <p><strong>Subtotal:</strong> $145.97</p>
                        <p><strong>Shipping:</strong> $0.00</p>
                        <p><strong>Tax:</strong> $14.60</p>
                        <p><strong>Total:</strong> $160.57</p>
                    </div>
                    
                    <div class="status-update-form">
                        <h3>Update Order Status</h3>
                        <form id="status-update-form">
                            <div class="form-group">
                                <label for="order-status">Status</label>
                                <select id="order-status">
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-notes">Notes</label>
                                <textarea id="status-notes" placeholder="Add notes about this status update"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small status-update" data-order-id="INF12345">Update Status</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Order Modal Functionality
            const viewOrderBtns = document.querySelectorAll('.view-order');
            const orderModal = document.getElementById('order-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Sample order data for demo purposes
            const orderData = {
                'INF12345': {
                    id: 'INF12345',
                    customer: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 15, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 1234)',
                    address: '123 Anime Street, Tokyo, JP 100-0001',
                    items: [
                        {name: 'Naruto Uzumaki Figure', image: 'images/1.jpg', price: 59.99, qty: 1, total: 59.99},
                        {name: 'Attack on Titan Hoodie', image: 'images/2.jpg', price: 39.99, qty: 1, total: 39.99},
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 145.97,
                    shipping: 0,
                    tax: 14.60,
                    total: 160.57
                },
                'INF12346': {
                    id: 'INF12346',
                    customer: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'processing',
                    payment: 'PayPal',
                    address: '456 Manga Road, Kyoto, JP 600-8216',
                    items: [
                        {name: 'One Piece Luffy Figure', image: 'images/4.jpg', price: 49.99, qty: 1, total: 49.99},
                        {name: 'Dragon Ball Z T-Shirt', image: 'images/2.jpg', price: 39.51, qty: 1, total: 39.51}
                    ],
                    subtotal: 89.50,
                    shipping: 0,
                    tax: 8.95,
                    total: 98.45
                },
                'INF12347': {
                    id: 'INF12347',
                    customer: 'Robert Johnson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'cancelled',
                    payment: 'Credit Card (•••• 5678)',
                    address: '789 Anime Avenue, Osaka, JP 530-0001',
                    items: [
                        {name: 'Demon Slayer Complete Manga Set', image: 'images/3.jpg', price: 120.00, qty: 1, total: 120.00},
                        {name: 'Anime Poster Collection', image: 'images/7.jpg', price: 22.95, qty: 3, total: 68.85},
                        {name: 'Anime Sticker Pack', image: 'images/1.jpg', price: 7.99, qty: 1, total: 7.99},
                        {name: 'Anime Keychain', image: 'images/2.jpg', price: 6.99, qty: 2, total: 13.98}
                    ],
                    subtotal: 210.82,
                    shipping: 0,
                    tax: 21.08,
                    total: 231.90
                },
                'INF12348': {
                    id: 'INF12348',
                    customer: 'Emily Davis',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 9012)',
                    address: '101 Otaku Lane, Tokyo, JP 100-0002',
                    items: [
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 45.99,
                    shipping: 0,
                    tax: 4.60,
                    total: 50.59
                },
                'INF12349': {
                    id: 'INF12349',
                    customer: 'Michael Wilson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'shipped',
                    payment: 'Credit Card (•••• 3456)',
                    address: '202 Manga Street, Kyoto, JP 600-8217',
                    items: [
                        {name: 'Attack on Titan Figure Set', image: 'images/1.jpg', price: 89.99, qty: 1, total: 89.99},
                        {name: 'Anime Themed Mug', image: 'images/2.jpg', price: 15.99, qty: 2, total: 31.98},
                        {name: 'Naruto Headband', image: 'images/3.jpg', price: 19.99, qty: 1, total: 19.99},
                        {name: 'Anime Wall Scroll', image: 'images/4.jpg', price: 33.29, qty: 1, total: 33.29}
                    ],
                    subtotal: 175.25,
                    shipping: 0,
                    tax: 17.53,
                    total: 192.78
                }
            };
            
            // Open modal when View Order button is clicked
            viewOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    const order = orderData[orderId];
                    
                    if (order) {
                        // Update modal with order details
                        document.getElementById('modal-order-id').textContent = '#' + order.id;
                        document.getElementById('modal-order-date').textContent = order.date;
                        document.getElementById('modal-payment-method').textContent = order.payment;
                        
                        const statusElement = document.getElementById('modal-order-status');
                        statusElement.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
                        statusElement.className = 'status ' + order.status;
                        
                        document.getElementById('modal-customer-name').textContent = order.customer;
                        document.getElementById('modal-customer-email').textContent = order.email;
                        document.getElementById('modal-customer-phone').textContent = order.phone;
                        document.getElementById('modal-shipping-address').textContent = order.address;
                        
                        // Update order items
                        const orderItemsContainer = document.getElementById('modal-order-items');
                        orderItemsContainer.innerHTML = '';
                        
                        order.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="${item.image}" alt="Product" class="order-item-image">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>${item.qty}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                        
                        // Update order totals
                        const orderTotal = document.querySelector('.order-total');
                        orderTotal.innerHTML = `
                            <p><strong>Subtotal:</strong> $${order.subtotal.toFixed(2)}</p>
                            <p><strong>Shipping:</strong> $${order.shipping.toFixed(2)}</p>
                            <p><strong>Tax:</strong> $${order.tax.toFixed(2)}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                        `;
                        
                        // Update status update button
                        document.querySelector('.status-update').setAttribute('data-order-id', order.id);
                        
                        // Set current status in dropdown
                        const statusSelect = document.getElementById('order-status');
                        for (let i = 0; i < statusSelect.options.length; i++) {
                            if (statusSelect.options[i].value === order.status) {
                                statusSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        // Clear notes
                        document.getElementById('status-notes').value = '';
                    }
                    
                    orderModal.classList.add('active');
                });
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            orderModal.addEventListener('click', function(e) {
                if (e.target === orderModal) {
                    orderModal.classList.remove('active');
                }
            });
            
            // Print order functionality (placeholder)
            const printOrderBtns = document.querySelectorAll('.print-order');
            
            printOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    alert(`Printing order #${orderId}...`);
                    // In a real application, this would trigger a print function
                });
            });
            
            // Status update functionality (placeholder)
            const statusUpdateBtn = document.querySelector('.status-update');
            
            statusUpdateBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const status = document.getElementById('order-status').value;
                const notes = document.getElementById('status-notes').value;
                
                alert(`Order #${orderId} status updated to ${status}${notes ? ' with notes' : ''}`);
                orderModal.classList.remove('active');
                
                // Update status in the table
                const orderRow = document.querySelector(`[data-id="${orderId}"]`).closest('tr');
                const statusCell = orderRow.querySelector('.status');
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'status ' + status;
            });
            
            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');
            
            function filterOrders() {
                const statusValue = statusFilter.value;
                const dateValue = dateFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                const rows = document.querySelectorAll('.orders-table tbody tr');
                
                rows.forEach(row => {
                    const orderStatus = row.querySelector('.status').textContent.toLowerCase();
                    const orderDate = row.querySelectorAll('td')[2].textContent; // Date column
                    const orderCustomer = row.querySelectorAll('td')[1].textContent.toLowerCase(); // Customer column
                    const orderId = row.querySelector('.order-id').textContent.toLowerCase();
                    
                    let showByStatus = statusValue === 'all' || orderStatus === statusValue;
                    let showByDate = true; // Default to true
                    
                    // Simple date filtering logic (could be more sophisticated in a real app)
                    if (dateValue !== 'all') {
                        const today = new Date();
                        const orderDateObj = new Date(orderDate);
                        
                        if (dateValue === 'today') {
                            showByDate = orderDateObj.toDateString() === today.toDateString();
                        } else if (dateValue === 'week') {
                            const weekAgo = new Date(today.setDate(today.getDate() - 7));
                            showByDate = orderDateObj >= weekAgo;
                        } else if (dateValue === 'month') {
                            const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
                            showByDate = orderDateObj >= monthAgo;
                        } else if (dateValue === 'year') {
                            const yearAgo = new Date(today.setFullYear(today.getFullYear() - 1));
                            showByDate = orderDateObj >= yearAgo;
                        }
                    }
                    
                    let showBySearch = searchValue === '' || 
                                      orderCustomer.includes(searchValue) || 
                                      orderId.includes(searchValue);
                    
                    if (showByStatus && showByDate && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
            searchButton.addEventListener('click', filterOrders);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterOrders();
                }
            });
            
            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');
            
            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>
                            <tr>
                                <td class="order-id">#INF12348</td>
                                <td>Emily Davis</td>
                                <td>June 13, 2023</td>
                                <td>1</td>
                                <td>$45.99</td>
                                <td><span class="status completed">Completed</span></td>
                                <td class="order-actions">
                                    <button title="View" class="view-order" data-id="INF12348"><i class="fas fa-eye"></i></button>
                                    <button title="Print" class="print-order" data-id="INF12348"><i class="fas fa-print"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td class="order-id">#INF12349</td>
                                <td>Michael Wilson</td>
                                <td>June 13, 2023</td>
                                <td>4</td>
                                <td>$175.25</td>
                                <td><span class="status shipped">Shipped</span></td>
                                <td class="order-actions">
                                    <button title="View" class="view-order" data-id="INF12349"><i class="fas fa-eye"></i></button>
                                    <button title="Print" class="print-order" data-id="INF12349"><i class="fas fa-print"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- Pagination -->
                    <div class="pagination">
                        <button><i class="fas fa-angle-left"></i></button>
                        <button class="active">1</button>
                        <button>2</button>
                        <button>3</button>
                        <button><i class="fas fa-angle-right"></i></button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Order Details Modal -->
        <div class="modal" id="order-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Order Details</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <div class="order-details-grid">
                        <div class="order-detail-block">
                            <h3>Order Information</h3>
                            <p><strong>Order ID:</strong> <span id="modal-order-id">#INF12345</span></p>
                            <p><strong>Date:</strong> <span id="modal-order-date">June 15, 2023</span></p>
                            <p><strong>Payment Method:</strong> <span id="modal-payment-method">Credit Card (•••• 1234)</span></p>
                            <p><strong>Status:</strong> <span id="modal-order-status" class="status completed">Completed</span></p>
                        </div>
                        <div class="order-detail-block">
                            <h3>Customer Information</h3>
                            <p><strong>Name:</strong> <span id="modal-customer-name">John Doe</span></p>
                            <p><strong>Email:</strong> <span id="modal-customer-email"><EMAIL></span></p>
                            <p><strong>Phone:</strong> <span id="modal-customer-phone">+****************</span></p>
                        </div>
                    </div>
                    
                    <div class="order-detail-block">
                        <h3>Shipping Address</h3>
                        <p id="modal-shipping-address">123 Anime Street, Tokyo, JP 100-0001</p>
                    </div>
                    
                    <h3>Order Items</h3>
                    <table class="order-items-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Price</th>
                                <th>Quantity</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody id="modal-order-items">
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/1.jpg" alt="Product" class="order-item-image">
                                        <span>Naruto Uzumaki Figure</span>
                                    </div>
                                </td>
                                <td>$59.99</td>
                                <td>1</td>
                                <td>$59.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/2.jpg" alt="Product" class="order-item-image">
                                        <span>Attack on Titan Hoodie</span>
                                    </div>
                                </td>
                                <td>$39.99</td>
                                <td>1</td>
                                <td>$39.99</td>
                            </tr>
                            <tr>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="images/3.jpg" alt="Product" class="order-item-image">
                                        <span>My Hero Academia Backpack</span>
                                    </div>
                                </td>
                                <td>$45.99</td>
                                <td>1</td>
                                <td>$45.99</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <div class="order-total">
                        <p><strong>Subtotal:</strong> $145.97</p>
                        <p><strong>Shipping:</strong> $0.00</p>
                        <p><strong>Tax:</strong> $14.60</p>
                        <p><strong>Total:</strong> $160.57</p>
                    </div>
                    
                    <div class="status-update-form">
                        <h3>Update Order Status</h3>
                        <form id="status-update-form">
                            <div class="form-group">
                                <label for="order-status">Status</label>
                                <select id="order-status">
                                    <option value="processing">Processing</option>
                                    <option value="shipped">Shipped</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="status-notes">Notes</label>
                                <textarea id="status-notes" placeholder="Add notes about this status update"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small status-update" data-order-id="INF12345">Update Status</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Order Modal Functionality
            const viewOrderBtns = document.querySelectorAll('.view-order');
            const orderModal = document.getElementById('order-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            
            // Sample order data for demo purposes
            const orderData = {
                'INF12345': {
                    id: 'INF12345',
                    customer: 'John Doe',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 15, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 1234)',
                    address: '123 Anime Street, Tokyo, JP 100-0001',
                    items: [
                        {name: 'Naruto Uzumaki Figure', image: 'images/1.jpg', price: 59.99, qty: 1, total: 59.99},
                        {name: 'Attack on Titan Hoodie', image: 'images/2.jpg', price: 39.99, qty: 1, total: 39.99},
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 145.97,
                    shipping: 0,
                    tax: 14.60,
                    total: 160.57
                },
                'INF12346': {
                    id: 'INF12346',
                    customer: 'Jane Smith',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'processing',
                    payment: 'PayPal',
                    address: '456 Manga Road, Kyoto, JP 600-8216',
                    items: [
                        {name: 'One Piece Luffy Figure', image: 'images/4.jpg', price: 49.99, qty: 1, total: 49.99},
                        {name: 'Dragon Ball Z T-Shirt', image: 'images/2.jpg', price: 39.51, qty: 1, total: 39.51}
                    ],
                    subtotal: 89.50,
                    shipping: 0,
                    tax: 8.95,
                    total: 98.45
                },
                'INF12347': {
                    id: 'INF12347',
                    customer: 'Robert Johnson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 14, 2023',
                    status: 'cancelled',
                    payment: 'Credit Card (•••• 5678)',
                    address: '789 Anime Avenue, Osaka, JP 530-0001',
                    items: [
                        {name: 'Demon Slayer Complete Manga Set', image: 'images/3.jpg', price: 120.00, qty: 1, total: 120.00},
                        {name: 'Anime Poster Collection', image: 'images/7.jpg', price: 22.95, qty: 3, total: 68.85},
                        {name: 'Anime Sticker Pack', image: 'images/1.jpg', price: 7.99, qty: 1, total: 7.99},
                        {name: 'Anime Keychain', image: 'images/2.jpg', price: 6.99, qty: 2, total: 13.98}
                    ],
                    subtotal: 210.82,
                    shipping: 0,
                    tax: 21.08,
                    total: 231.90
                },
                'INF12348': {
                    id: 'INF12348',
                    customer: 'Emily Davis',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'completed',
                    payment: 'Credit Card (•••• 9012)',
                    address: '101 Otaku Lane, Tokyo, JP 100-0002',
                    items: [
                        {name: 'My Hero Academia Backpack', image: 'images/3.jpg', price: 45.99, qty: 1, total: 45.99}
                    ],
                    subtotal: 45.99,
                    shipping: 0,
                    tax: 4.60,
                    total: 50.59
                },
                'INF12349': {
                    id: 'INF12349',
                    customer: 'Michael Wilson',
                    email: '<EMAIL>',
                    phone: '+****************',
                    date: 'June 13, 2023',
                    status: 'shipped',
                    payment: 'Credit Card (•••• 3456)',
                    address: '202 Manga Street, Kyoto, JP 600-8217',
                    items: [
                        {name: 'Attack on Titan Figure Set', image: 'images/1.jpg', price: 89.99, qty: 1, total: 89.99},
                        {name: 'Anime Themed Mug', image: 'images/2.jpg', price: 15.99, qty: 2, total: 31.98},
                        {name: 'Naruto Headband', image: 'images/3.jpg', price: 19.99, qty: 1, total: 19.99},
                        {name: 'Anime Wall Scroll', image: 'images/4.jpg', price: 33.29, qty: 1, total: 33.29}
                    ],
                    subtotal: 175.25,
                    shipping: 0,
                    tax: 17.53,
                    total: 192.78
                }
            };
            
            // Open modal when View Order button is clicked
            viewOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    const order = orderData[orderId];
                    
                    if (order) {
                        // Update modal with order details
                        document.getElementById('modal-order-id').textContent = '#' + order.id;
                        document.getElementById('modal-order-date').textContent = order.date;
                        document.getElementById('modal-payment-method').textContent = order.payment;
                        
                        const statusElement = document.getElementById('modal-order-status');
                        statusElement.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
                        statusElement.className = 'status ' + order.status;
                        
                        document.getElementById('modal-customer-name').textContent = order.customer;
                        document.getElementById('modal-customer-email').textContent = order.email;
                        document.getElementById('modal-customer-phone').textContent = order.phone;
                        document.getElementById('modal-shipping-address').textContent = order.address;
                        
                        // Update order items
                        const orderItemsContainer = document.getElementById('modal-order-items');
                        orderItemsContainer.innerHTML = '';
                        
                        order.items.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <img src="${item.image}" alt="Product" class="order-item-image">
                                        <span>${item.name}</span>
                                    </div>
                                </td>
                                <td>$${item.price.toFixed(2)}</td>
                                <td>${item.qty}</td>
                                <td>$${item.total.toFixed(2)}</td>
                            `;
                            orderItemsContainer.appendChild(row);
                        });
                        
                        // Update order totals
                        const orderTotal = document.querySelector('.order-total');
                        orderTotal.innerHTML = `
                            <p><strong>Subtotal:</strong> $${order.subtotal.toFixed(2)}</p>
                            <p><strong>Shipping:</strong> $${order.shipping.toFixed(2)}</p>
                            <p><strong>Tax:</strong> $${order.tax.toFixed(2)}</p>
                            <p><strong>Total:</strong> $${order.total.toFixed(2)}</p>
                        `;
                        
                        // Update status update button
                        document.querySelector('.status-update').setAttribute('data-order-id', order.id);
                        
                        // Set current status in dropdown
                        const statusSelect = document.getElementById('order-status');
                        for (let i = 0; i < statusSelect.options.length; i++) {
                            if (statusSelect.options[i].value === order.status) {
                                statusSelect.selectedIndex = i;
                                break;
                            }
                        }
                        
                        // Clear notes
                        document.getElementById('status-notes').value = '';
                    }
                    
                    orderModal.classList.add('active');
                });
            });
            
            // Close modal when close button or cancel button is clicked
            modalClose.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            cancelBtn.addEventListener('click', function() {
                orderModal.classList.remove('active');
            });
            
            // Close modal when clicking outside the modal content
            orderModal.addEventListener('click', function(e) {
                if (e.target === orderModal) {
                    orderModal.classList.remove('active');
                }
            });
            
            // Print order functionality (placeholder)
            const printOrderBtns = document.querySelectorAll('.print-order');
            
            printOrderBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const orderId = this.getAttribute('data-id');
                    alert(`Printing order #${orderId}...`);
                    // In a real application, this would trigger a print function
                });
            });
            
            // Status update functionality (placeholder)
            const statusUpdateBtn = document.querySelector('.status-update');
            
            statusUpdateBtn.addEventListener('click', function() {
                const orderId = this.getAttribute('data-order-id');
                const status = document.getElementById('order-status').value;
                const notes = document.getElementById('status-notes').value;
                
                alert(`Order #${orderId} status updated to ${status}${notes ? ' with notes' : ''}`);
                orderModal.classList.remove('active');
                
                // Update status in the table
                const orderRow = document.querySelector(`[data-id="${orderId}"]`).closest('tr');
                const statusCell = orderRow.querySelector('.status');
                statusCell.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                statusCell.className = 'status ' + status;
            });
            
            // Filter functionality
            const statusFilter = document.getElementById('status-filter');
            const dateFilter = document.getElementById('date-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');
            
            function filterOrders() {
                const statusValue = statusFilter.value;
                const dateValue = dateFilter.value;
                const searchValue = searchBox.value.toLowerCase();
                
                const rows = document.querySelectorAll('.orders-table tbody tr');
                
                rows.forEach(row => {
                    const orderStatus = row.querySelector('.status').textContent.toLowerCase();
                    const orderDate = row.querySelectorAll('td')[2].textContent; // Date column
                    const orderCustomer = row.querySelectorAll('td')[1].textContent.toLowerCase(); // Customer column
                    const orderId = row.querySelector('.order-id').textContent.toLowerCase();
                    
                    let showByStatus = statusValue === 'all' || orderStatus === statusValue;
                    let showByDate = true; // Default to true
                    
                    // Simple date filtering logic (could be more sophisticated in a real app)
                    if (dateValue !== 'all') {
                        const today = new Date();
                        const orderDateObj = new Date(orderDate);
                        
                        if (dateValue === 'today') {
                            showByDate = orderDateObj.toDateString() === today.toDateString();
                        } else if (dateValue === 'week') {
                            const weekAgo = new Date(today.setDate(today.getDate() - 7));
                            showByDate = orderDateObj >= weekAgo;
                        } else if (dateValue === 'month') {
                            const monthAgo = new Date(today.setMonth(today.getMonth() - 1));
                            showByDate = orderDateObj >= monthAgo;
                        } else if (dateValue === 'year') {
                            const yearAgo = new Date(today.setFullYear(today.getFullYear() - 1));
                            showByDate = orderDateObj >= yearAgo;
                        }
                    }
                    
                    let showBySearch = searchValue === '' || 
                                      orderCustomer.includes(searchValue) || 
                                      orderId.includes(searchValue);
                    
                    if (showByStatus && showByDate && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }
            
            statusFilter.addEventListener('change', filterOrders);
            dateFilter.addEventListener('change', filterOrders);
            searchButton.addEventListener('click', filterOrders);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterOrders();
                }
            });
            
            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');
            
            mobileMenu.addEventListener('click', function() {
                adminSidebar.classList.toggle('active');
            });
        });
    </script>