<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
            min-height: calc(100vh - 76px);
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Analytics Filters */
        .analytics-filters {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Analytics Stats */
        .analytics-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-info p {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .stat-change {
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 5px;
        }

        .stat-change.positive {
            color: #48c774;
        }

        .stat-change.negative {
            color: var(--secondary-color);
        }

        /* Charts Grid */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .chart-period {
            display: flex;
            gap: 5px;
        }

        .chart-period button {
            padding: 5px 10px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .chart-period button.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        .chart-container.small {
            height: 200px;
        }

        /* Tables */
        .analytics-table {
            width: 100%;
            border-collapse: collapse;
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
            margin-bottom: 20px;
        }

        .analytics-table th,
        .analytics-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .analytics-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }

        .analytics-table tr:last-child td {
            border-bottom: none;
        }

        .product-rank {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .rank-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .product-image {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }

            .charts-grid {
                grid-template-columns: 1fr;
            }

            .analytics-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .admin-content {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .analytics-stats {
                grid-template-columns: 1fr;
            }

            .chart-period {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>

                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="admin-analytics.html" class="active"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>

            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-section" id="analytics-section">
                    <div class="admin-header">
                        <h1>Analytics Dashboard</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export Report</button>
                            <button class="btn btn-small"><i class="fas fa-calendar"></i> Schedule Report</button>
                        </div>
                    </div>

                    <!-- Analytics Filters -->
                    <div class="analytics-filters">
                        <div class="filter-group">
                            <label for="date-range">Date Range:</label>
                            <select id="date-range">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 3 months</option>
                                <option value="365">Last year</option>
                                <option value="custom">Custom range</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="category-analytics">Category:</label>
                            <select id="category-analytics">
                                <option value="all">All Categories</option>
                                <option value="figures">Figures</option>
                                <option value="clothing">Clothing</option>
                                <option value="accessories">Accessories</option>
                                <option value="manga">Manga</option>
                            </select>
                        </div>
                    </div>

                    <!-- Analytics Stats -->
                    <div class="analytics-stats">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #6a3de8, #8a5cf5);">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <h3>$24,580</h3>
                                <p>Total Revenue</p>
                                <div class="stat-change positive">+12.5% from last month</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #48c774, #5dd68d);">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-info">
                                <h3>1,247</h3>
                                <p>Total Orders</p>
                                <div class="stat-change positive">+8.3% from last month</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ffbe0b, #ffd24c);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-info">
                                <h3>892</h3>
                                <p>New Customers</p>
                                <div class="stat-change positive">+15.7% from last month</div>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ff6b6b, #ff8585);">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-info">
                                <h3>3.2%</h3>
                                <p>Conversion Rate</p>
                                <div class="stat-change negative">-0.5% from last month</div>
                            </div>
                        </div>
                    </div>

                    <!-- Charts Grid -->
                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Revenue Trend</h3>
                                <div class="chart-period">
                                    <button class="period-btn" data-period="7">7D</button>
                                    <button class="period-btn active" data-period="30">30D</button>
                                    <button class="period-btn" data-period="90">90D</button>
                                    <button class="period-btn" data-period="365">1Y</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="revenueChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Sales by Category</h3>
                            </div>
                            <div class="chart-container small">
                                <canvas id="categoryChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <div class="charts-grid">
                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Customer Growth</h3>
                                <div class="chart-period">
                                    <button class="period-btn" data-period="7">7D</button>
                                    <button class="period-btn active" data-period="30">30D</button>
                                    <button class="period-btn" data-period="90">90D</button>
                                </div>
                            </div>
                            <div class="chart-container">
                                <canvas id="customerChart"></canvas>
                            </div>
                        </div>

                        <div class="chart-card">
                            <div class="chart-header">
                                <h3>Top Products</h3>
                            </div>
                            <table class="analytics-table">
                                <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Product</th>
                                        <th>Sales</th>
                                        <th>Revenue</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <div class="product-rank">
                                                <div class="rank-number">1</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="product-rank">
                                                <img src="images/1.jpg" alt="Product" class="product-image">
                                                <span class="product-name">Naruto Uzumaki Figure</span>
                                            </div>
                                        </td>
                                        <td>156</td>
                                        <td>$9,360</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="product-rank">
                                                <div class="rank-number">2</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="product-rank">
                                                <img src="images/7.jpg" alt="Product" class="product-image">
                                                <span class="product-name">One Piece Manga Collection</span>
                                            </div>
                                        </td>
                                        <td>89</td>
                                        <td>$10,680</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="product-rank">
                                                <div class="rank-number">3</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="product-rank">
                                                <img src="images/2.jpg" alt="Product" class="product-image">
                                                <span class="product-name">Attack on Titan Hoodie</span>
                                            </div>
                                        </td>
                                        <td>67</td>
                                        <td>$2,679</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="product-rank">
                                                <div class="rank-number">4</div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="product-rank">
                                                <img src="images/3.jpg" alt="Product" class="product-image">
                                                <span class="product-name">My Hero Academia Backpack</span>
                                            </div>
                                        </td>
                                        <td>45</td>
                                        <td>$2,070</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Chart.js configuration
            Chart.defaults.color = getComputedStyle(document.documentElement).getPropertyValue('--text-color').trim();
            Chart.defaults.borderColor = getComputedStyle(document.documentElement).getPropertyValue('--border-color').trim();

            // Revenue Chart
            const revenueCtx = document.getElementById('revenueChart').getContext('2d');
            const revenueChart = new Chart(revenueCtx, {
                type: 'line',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    datasets: [{
                        label: 'Revenue',
                        data: [5200, 6800, 7200, 8400],
                        borderColor: '#6a3de8',
                        backgroundColor: 'rgba(106, 61, 232, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });

            // Category Chart
            const categoryCtx = document.getElementById('categoryChart').getContext('2d');
            const categoryChart = new Chart(categoryCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Figures', 'Manga', 'Clothing', 'Accessories'],
                    datasets: [{
                        data: [35, 30, 20, 15],
                        backgroundColor: [
                            '#6a3de8',
                            '#48c774',
                            '#ff6b6b',
                            '#ffbe0b'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Customer Growth Chart
            const customerCtx = document.getElementById('customerChart').getContext('2d');
            const customerChart = new Chart(customerCtx, {
                type: 'bar',
                data: {
                    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                    datasets: [{
                        label: 'New Customers',
                        data: [180, 220, 250, 280],
                        backgroundColor: '#48c774',
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Period button functionality
            document.querySelectorAll('.period-btn').forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from siblings
                    this.parentNode.querySelectorAll('.period-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Update chart data based on period (placeholder)
                    const period = this.getAttribute('data-period');
                    console.log('Updating charts for period:', period);
                });
            });

            // Date range filter
            document.getElementById('date-range').addEventListener('change', function() {
                const range = this.value;
                console.log('Date range changed to:', range);
                // Update all charts and stats based on selected range
            });

            // Category filter
            document.getElementById('category-analytics').addEventListener('change', function() {
                const category = this.value;
                console.log('Category filter changed to:', category);
                // Filter analytics data by category
            });

            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');

            if (mobileMenu && adminSidebar) {
                mobileMenu.addEventListener('click', function() {
                    adminSidebar.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
