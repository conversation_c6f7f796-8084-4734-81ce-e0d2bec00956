<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory - Admin Panel - Infinite Shadow</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Admin Panel Styles */
        .admin-container {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 76px);
        }
        
        .admin-sidebar {
            background-color: var(--card-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px 0;
            box-shadow: 2px 0 10px var(--shadow-color);
        }
        
        .admin-sidebar .user-info {
            padding: 0 20px 20px;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .admin-sidebar .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }
        
        .admin-sidebar .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .admin-sidebar .user-role {
            font-size: 0.9rem;
            color: var(--primary-color);
        }
        
        .admin-nav {
            list-style: none;
            padding: 0;
        }
        
        .admin-nav li {
            margin-bottom: 5px;
        }
        
        .admin-nav a {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: var(--text-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background-color: rgba(106, 61, 232, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }
        
        .admin-nav a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }
        
        .admin-content {
            padding: 30px;
            background-color: var(--bg-color);
            overflow-y: auto;
            min-height: calc(100vh - 76px);
        }
        
        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }
        
        .admin-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .admin-header .admin-actions {
            display: flex;
            gap: 10px;
        }

        /* Inventory Stats Cards */
        .inventory-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: var(--card-bg);
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 5px 15px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .stat-info h3 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-info p {
            opacity: 0.7;
            font-size: 0.9rem;
        }

        /* Inventory Filters */
        .inventory-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-group select,
        .search-box input {
            padding: 8px 12px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-box button {
            padding: 8px 12px;
            border: none;
            background-color: var(--primary-color);
            color: white;
            border-radius: 0 5px 5px 0;
            cursor: pointer;
        }

        /* Inventory Table */
        .inventory-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            background-color: var(--card-bg);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px var(--shadow-color);
        }

        .inventory-table th,
        .inventory-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .inventory-table th {
            background-color: var(--bg-color);
            font-weight: 600;
        }

        .inventory-table tr:last-child td {
            border-bottom: none;
        }

        .product-image {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            object-fit: cover;
        }

        .product-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .stock-level {
            font-weight: 600;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.9rem;
            text-align: center;
            display: inline-block;
            min-width: 80px;
        }

        .stock-level.high {
            background-color: rgba(72, 199, 116, 0.2);
            color: #48c774;
        }

        .stock-level.medium {
            background-color: rgba(255, 190, 11, 0.2);
            color: var(--accent-color);
        }

        .stock-level.low {
            background-color: rgba(255, 107, 107, 0.2);
            color: var(--secondary-color);
        }

        .stock-level.out {
            background-color: rgba(128, 128, 128, 0.2);
            color: #808080;
        }

        .inventory-actions {
            display: flex;
            gap: 10px;
        }

        .inventory-actions button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
            padding: 5px;
        }

        .inventory-actions button:hover {
            opacity: 1;
            color: var(--primary-color);
        }

        /* Stock Update Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            overflow-y: auto;
            padding: 50px 0;
        }

        .modal.active {
            display: block;
        }

        .modal-content {
            background-color: var(--card-bg);
            border-radius: 10px;
            max-width: 500px;
            margin: 0 auto;
            box-shadow: 0 5px 20px var(--shadow-color);
            position: relative;
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text-color);
            opacity: 0.7;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            opacity: 1;
            color: var(--secondary-color);
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border-radius: 5px;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: 'Poppins', sans-serif;
        }

        .stock-adjustment {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .stock-adjustment button {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 1px solid var(--border-color);
            background-color: var(--primary-color);
            color: white;
            cursor: pointer;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .stock-adjustment button:hover {
            background-color: #5a2bc7;
        }

        .stock-adjustment input {
            width: 80px;
            text-align: center;
            margin: 0;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid var(--border-color);
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-cancel {
            background-color: var(--bg-color);
            color: var(--text-color);
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .admin-container {
                grid-template-columns: 1fr;
            }
            
            .admin-sidebar {
                display: none;
                position: fixed;
                top: 76px;
                left: 0;
                width: 100%;
                height: calc(100vh - 76px);
                z-index: 1000;
            }
            
            .admin-sidebar.active {
                display: block;
            }

            .inventory-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .inventory-table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
            }

            .inventory-table th,
            .inventory-table td {
                min-width: 120px;
            }

            .admin-content {
                padding: 15px;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                margin: 0 10px;
                max-width: calc(100% - 20px);
            }

            .modal-footer {
                flex-direction: column;
            }

            .modal-footer button {
                width: 100%;
            }

            .inventory-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="dark-mode">
    <header>
        <div class="container">
            <div class="logo">
                <h1>Infinite<span>Shadow</span></h1>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="shop.html">Shop</a></li>
                    <li><a href="admin.html" class="active">Admin</a></li>
                </ul>
            </nav>
            <div class="header-icons">
                <div class="theme-toggle">
                    <i class="fas fa-sun"></i>
                </div>
                <div class="mobile-menu">
                    <i class="fas fa-bars"></i>
                </div>
            </div>
        </div>
    </header>

    <main>
        <div class="admin-container">
            <!-- Admin Sidebar -->
            <div class="admin-sidebar">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="user-name">Admin User</h3>
                    <p class="user-role">Administrator</p>
                </div>

                <ul class="admin-nav">
                    <li><a href="admin.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="admin-products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="admin-orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="#" data-section="customers"><i class="fas fa-users"></i> Customers</a></li>
                    <li><a href="admin-categories.html"><i class="fas fa-tags"></i> Categories</a></li>
                    <li><a href="admin-inventory.html" class="active"><i class="fas fa-warehouse"></i> Inventory</a></li>
                    <li><a href="admin-analytics.html"><i class="fas fa-chart-line"></i> Analytics</a></li>
                    <li><a href="admin-settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="login.html"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </div>

            <!-- Admin Content -->
            <div class="admin-content">
                <div class="admin-section" id="inventory-section">
                    <div class="admin-header">
                        <h1>Inventory Management</h1>
                        <div class="admin-actions">
                            <button class="btn btn-small"><i class="fas fa-plus"></i> Bulk Update</button>
                            <button class="btn btn-small"><i class="fas fa-download"></i> Export</button>
                        </div>
                    </div>

                    <!-- Inventory Stats -->
                    <div class="inventory-stats">
                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #48c774, #5dd68d);">
                                <i class="fas fa-boxes"></i>
                            </div>
                            <div class="stat-info">
                                <h3>119</h3>
                                <p>Total Products</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ffbe0b, #ffd24c);">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <h3>8</h3>
                                <p>Low Stock Items</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #ff6b6b, #ff8585);">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3>3</h3>
                                <p>Out of Stock</p>
                            </div>
                        </div>

                        <div class="stat-card">
                            <div class="stat-icon" style="background: linear-gradient(135deg, #6a3de8, #8a5cf5);">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-info">
                                <h3>$45,230</h3>
                                <p>Total Inventory Value</p>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Filters -->
                    <div class="inventory-filters">
                        <div class="filter-group">
                            <label for="category-filter">Category:</label>
                            <select id="category-filter">
                                <option value="all">All Categories</option>
                                <option value="figures">Figures</option>
                                <option value="clothing">Clothing</option>
                                <option value="accessories">Accessories</option>
                                <option value="manga">Manga</option>
                            </select>
                        </div>

                        <div class="filter-group">
                            <label for="stock-filter">Stock Level:</label>
                            <select id="stock-filter">
                                <option value="all">All Levels</option>
                                <option value="high">High Stock (>20)</option>
                                <option value="medium">Medium Stock (10-20)</option>
                                <option value="low">Low Stock (1-9)</option>
                                <option value="out">Out of Stock</option>
                            </select>
                        </div>

                        <div class="search-box">
                            <input type="text" placeholder="Search products...">
                            <button><i class="fas fa-search"></i></button>
                        </div>
                    </div>

                    <!-- Inventory Table -->
                    <table class="inventory-table">
                        <thead>
                            <tr>
                                <th>Image</th>
                                <th>Product</th>
                                <th>SKU</th>
                                <th>Category</th>
                                <th>Current Stock</th>
                                <th>Reserved</th>
                                <th>Available</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><img src="images/1.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Naruto Uzumaki Figure</td>
                                <td>SKU-001</td>
                                <td>Figures</td>
                                <td>25</td>
                                <td>3</td>
                                <td>22</td>
                                <td><span class="stock-level high">High</span></td>
                                <td class="inventory-actions">
                                    <button title="Update Stock" class="update-stock" data-product="Naruto Uzumaki Figure" data-current="25"><i class="fas fa-edit"></i></button>
                                    <button title="View History" class="view-history"><i class="fas fa-history"></i></button>
                                    <button title="Set Alert" class="set-alert"><i class="fas fa-bell"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/2.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Attack on Titan Hoodie</td>
                                <td>SKU-002</td>
                                <td>Clothing</td>
                                <td>8</td>
                                <td>2</td>
                                <td>6</td>
                                <td><span class="stock-level low">Low</span></td>
                                <td class="inventory-actions">
                                    <button title="Update Stock" class="update-stock" data-product="Attack on Titan Hoodie" data-current="8"><i class="fas fa-edit"></i></button>
                                    <button title="View History" class="view-history"><i class="fas fa-history"></i></button>
                                    <button title="Set Alert" class="set-alert"><i class="fas fa-bell"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/3.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">My Hero Academia Backpack</td>
                                <td>SKU-003</td>
                                <td>Accessories</td>
                                <td>15</td>
                                <td>1</td>
                                <td>14</td>
                                <td><span class="stock-level medium">Medium</span></td>
                                <td class="inventory-actions">
                                    <button title="Update Stock" class="update-stock" data-product="My Hero Academia Backpack" data-current="15"><i class="fas fa-edit"></i></button>
                                    <button title="View History" class="view-history"><i class="fas fa-history"></i></button>
                                    <button title="Set Alert" class="set-alert"><i class="fas fa-bell"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/4.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">Demon Slayer Poster Set</td>
                                <td>SKU-004</td>
                                <td>Accessories</td>
                                <td>0</td>
                                <td>0</td>
                                <td>0</td>
                                <td><span class="stock-level out">Out of Stock</span></td>
                                <td class="inventory-actions">
                                    <button title="Update Stock" class="update-stock" data-product="Demon Slayer Poster Set" data-current="0"><i class="fas fa-edit"></i></button>
                                    <button title="View History" class="view-history"><i class="fas fa-history"></i></button>
                                    <button title="Set Alert" class="set-alert"><i class="fas fa-bell"></i></button>
                                </td>
                            </tr>
                            <tr>
                                <td><img src="images/7.jpg" alt="Product" class="product-image"></td>
                                <td class="product-name">One Piece Manga Collection</td>
                                <td>SKU-005</td>
                                <td>Manga</td>
                                <td>32</td>
                                <td>5</td>
                                <td>27</td>
                                <td><span class="stock-level high">High</span></td>
                                <td class="inventory-actions">
                                    <button title="Update Stock" class="update-stock" data-product="One Piece Manga Collection" data-current="32"><i class="fas fa-edit"></i></button>
                                    <button title="View History" class="view-history"><i class="fas fa-history"></i></button>
                                    <button title="Set Alert" class="set-alert"><i class="fas fa-bell"></i></button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Stock Update Modal -->
        <div class="modal" id="stock-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Update Stock</h2>
                    <button class="modal-close"><i class="fas fa-times"></i></button>
                </div>
                <div class="modal-body">
                    <form id="stock-form">
                        <div class="form-group">
                            <label for="product-name-display">Product</label>
                            <input type="text" id="product-name-display" readonly>
                        </div>
                        <div class="form-group">
                            <label for="current-stock">Current Stock</label>
                            <input type="number" id="current-stock" readonly>
                        </div>
                        <div class="form-group">
                            <label for="stock-adjustment-type">Adjustment Type</label>
                            <select id="stock-adjustment-type">
                                <option value="add">Add Stock</option>
                                <option value="remove">Remove Stock</option>
                                <option value="set">Set Stock</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="adjustment-quantity">Quantity</label>
                            <div class="stock-adjustment">
                                <button type="button" class="decrease-qty"><i class="fas fa-minus"></i></button>
                                <input type="number" id="adjustment-quantity" min="0" value="1">
                                <button type="button" class="increase-qty"><i class="fas fa-plus"></i></button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="adjustment-reason">Reason</label>
                            <select id="adjustment-reason">
                                <option value="restock">Restock</option>
                                <option value="sale">Sale</option>
                                <option value="damage">Damage/Loss</option>
                                <option value="return">Return</option>
                                <option value="correction">Inventory Correction</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="adjustment-notes">Notes (Optional)</label>
                            <textarea id="adjustment-notes" placeholder="Add any additional notes..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-small btn-cancel">Cancel</button>
                    <button class="btn btn-small" form="stock-form">Update Stock</button>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2>Infinite<span>Shadow</span></h2>
                    <p>Your ultimate anime merchandise store</p>
                </div>
                <div class="footer-links">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="index.html">Home</a></li>
                        <li><a href="shop.html">Shop</a></li>
                        <li><a href="admin.html">Admin</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h3>Categories</h3>
                    <ul>
                        <li><a href="shop.html?category=figures">Figures</a></li>
                        <li><a href="shop.html?category=clothing">Clothing</a></li>
                        <li><a href="shop.html?category=accessories">Accessories</a></li>
                        <li><a href="shop.html?category=manga">Manga</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3>Contact Us</h3>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fas fa-phone"></i> +****************</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 InfiniteShadow. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script src="js/admin.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Stock Update Modal Functionality
            const updateStockBtns = document.querySelectorAll('.update-stock');
            const stockModal = document.getElementById('stock-modal');
            const modalClose = document.querySelector('.modal-close');
            const cancelBtn = document.querySelector('.btn-cancel');
            const stockForm = document.getElementById('stock-form');

            // Quantity adjustment buttons
            const decreaseBtn = document.querySelector('.decrease-qty');
            const increaseBtn = document.querySelector('.increase-qty');
            const quantityInput = document.getElementById('adjustment-quantity');

            decreaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 0;
                if (currentValue > 0) {
                    quantityInput.value = currentValue - 1;
                }
            });

            increaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value) || 0;
                quantityInput.value = currentValue + 1;
            });

            // Open stock update modal
            updateStockBtns.forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.getAttribute('data-product');
                    const currentStock = this.getAttribute('data-current');

                    document.getElementById('product-name-display').value = productName;
                    document.getElementById('current-stock').value = currentStock;
                    document.getElementById('adjustment-quantity').value = 1;
                    document.getElementById('stock-adjustment-type').value = 'add';
                    document.getElementById('adjustment-reason').value = 'restock';
                    document.getElementById('adjustment-notes').value = '';

                    stockModal.classList.add('active');
                });
            });

            // Close modal functionality
            modalClose.addEventListener('click', function() {
                stockModal.classList.remove('active');
            });

            cancelBtn.addEventListener('click', function() {
                stockModal.classList.remove('active');
            });

            stockModal.addEventListener('click', function(e) {
                if (e.target === stockModal) {
                    stockModal.classList.remove('active');
                }
            });

            // Stock form submission
            stockForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const productName = document.getElementById('product-name-display').value;
                const currentStock = parseInt(document.getElementById('current-stock').value);
                const adjustmentType = document.getElementById('stock-adjustment-type').value;
                const quantity = parseInt(document.getElementById('adjustment-quantity').value);
                const reason = document.getElementById('adjustment-reason').value;

                let newStock;
                switch(adjustmentType) {
                    case 'add':
                        newStock = currentStock + quantity;
                        break;
                    case 'remove':
                        newStock = Math.max(0, currentStock - quantity);
                        break;
                    case 'set':
                        newStock = quantity;
                        break;
                }

                // Update the table row
                const button = document.querySelector(`[data-product="${productName}"]`);
                const row = button.closest('tr');
                const stockCell = row.cells[4]; // Current Stock column
                const availableCell = row.cells[6]; // Available column
                const statusCell = row.cells[7]; // Status column

                stockCell.textContent = newStock;
                availableCell.textContent = newStock; // Simplified - in real app would account for reserved

                // Update status based on new stock level
                const statusSpan = statusCell.querySelector('.stock-level');
                statusSpan.className = 'stock-level';

                if (newStock === 0) {
                    statusSpan.classList.add('out');
                    statusSpan.textContent = 'Out of Stock';
                } else if (newStock <= 9) {
                    statusSpan.classList.add('low');
                    statusSpan.textContent = 'Low';
                } else if (newStock <= 20) {
                    statusSpan.classList.add('medium');
                    statusSpan.textContent = 'Medium';
                } else {
                    statusSpan.classList.add('high');
                    statusSpan.textContent = 'High';
                }

                // Update button data attribute
                button.setAttribute('data-current', newStock);

                alert(`Stock updated successfully! ${productName} now has ${newStock} units.`);
                stockModal.classList.remove('active');
            });

            // Filter functionality
            const categoryFilter = document.getElementById('category-filter');
            const stockFilter = document.getElementById('stock-filter');
            const searchBox = document.querySelector('.search-box input');
            const searchButton = document.querySelector('.search-box button');

            function filterInventory() {
                const categoryValue = categoryFilter.value;
                const stockValue = stockFilter.value;
                const searchValue = searchBox.value.toLowerCase();

                const rows = document.querySelectorAll('.inventory-table tbody tr');

                rows.forEach(row => {
                    const productCategory = row.cells[3].textContent.toLowerCase();
                    const productName = row.cells[1].textContent.toLowerCase();
                    const sku = row.cells[2].textContent.toLowerCase();
                    const currentStock = parseInt(row.cells[4].textContent);

                    let showByCategory = categoryValue === 'all' || productCategory === categoryValue;
                    let showByStock = true;

                    if (stockValue !== 'all') {
                        switch(stockValue) {
                            case 'high':
                                showByStock = currentStock > 20;
                                break;
                            case 'medium':
                                showByStock = currentStock >= 10 && currentStock <= 20;
                                break;
                            case 'low':
                                showByStock = currentStock >= 1 && currentStock <= 9;
                                break;
                            case 'out':
                                showByStock = currentStock === 0;
                                break;
                        }
                    }

                    let showBySearch = searchValue === '' ||
                                      productName.includes(searchValue) ||
                                      sku.includes(searchValue);

                    if (showByCategory && showByStock && showBySearch) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            categoryFilter.addEventListener('change', filterInventory);
            stockFilter.addEventListener('change', filterInventory);
            searchButton.addEventListener('click', filterInventory);
            searchBox.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    filterInventory();
                }
            });

            // View history and set alert functionality (placeholders)
            document.querySelectorAll('.view-history').forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.closest('tr').cells[1].textContent;
                    alert(`Viewing stock history for ${productName}...`);
                });
            });

            document.querySelectorAll('.set-alert').forEach(button => {
                button.addEventListener('click', function() {
                    const productName = this.closest('tr').cells[1].textContent;
                    const threshold = prompt(`Set low stock alert threshold for ${productName}:`, '5');
                    if (threshold !== null) {
                        alert(`Low stock alert set at ${threshold} units for ${productName}`);
                    }
                });
            });

            // Mobile menu toggle for admin sidebar
            const mobileMenu = document.querySelector('.mobile-menu');
            const adminSidebar = document.querySelector('.admin-sidebar');

            if (mobileMenu && adminSidebar) {
                mobileMenu.addEventListener('click', function() {
                    adminSidebar.classList.toggle('active');
                });
            }
        });
    </script>
</body>
</html>
